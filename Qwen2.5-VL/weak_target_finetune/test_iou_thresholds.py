#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同IoU阈值下data_15的性能表现
找到最合适的IoU阈值
"""

import json
import sys
from pathlib import Path
from collections import defaultdict

# 添加父目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from evaluate_finetuned_improved import load_yolo_ground_truth_with_natural_sort, load_class_map

def calculate_iou(box1, box2):
    """计算IoU"""
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2
    
    inter_x1 = max(x1_1, x1_2)
    inter_y1 = max(y1_1, y1_2)
    inter_x2 = min(x2_1, x2_2)
    inter_y2 = min(y2_1, y2_2)
    
    if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
        return 0.0
    
    inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = area1 + area2 - inter_area
    
    return inter_area / union_area if union_area > 0 else 0.0

def evaluate_with_iou_threshold(detections, ground_truths, iou_threshold):
    """使用指定IoU阈值评估性能"""
    # 按帧分组
    det_by_frame = defaultdict(list)
    gt_by_frame = defaultdict(list)
    
    for det in detections:
        det_by_frame[det['frame_id']].append(det)
    
    for gt in ground_truths:
        gt_by_frame[gt.frame_id].append(gt)
    
    # 统计
    total_tp = 0
    total_fp = 0
    total_fn = 0
    total_matches = 0
    total_detections = len(detections)
    total_ground_truths = len(ground_truths)
    
    # 获取所有帧
    all_frames = set(det_by_frame.keys()) | set(gt_by_frame.keys())
    
    for frame_id in all_frames:
        frame_dets = det_by_frame[frame_id]
        frame_gts = gt_by_frame[frame_id]
        
        # 匹配该帧的检测和标注
        matched_dets = set()
        matched_gts = set()
        
        for i, det in enumerate(frame_dets):
            best_iou = 0
            best_gt_idx = -1
            
            for j, gt in enumerate(frame_gts):
                if j in matched_gts:
                    continue
                
                # 只匹配相同类别
                if det['label'] != gt.label:
                    continue
                
                iou = calculate_iou(det['bbox'], gt.bbox)
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = j
            
            if best_iou >= iou_threshold and best_gt_idx not in matched_gts:
                matched_dets.add(i)
                matched_gts.add(best_gt_idx)
                total_matches += 1
        
        # 统计TP, FP, FN
        frame_tp = len(matched_dets)
        frame_fp = len(frame_dets) - len(matched_dets)
        frame_fn = len(frame_gts) - len(matched_gts)
        
        total_tp += frame_tp
        total_fp += frame_fp
        total_fn += frame_fn
    
    # 计算指标
    precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0.0
    recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0
    f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
    
    return {
        'iou_threshold': iou_threshold,
        'tp': total_tp,
        'fp': total_fp,
        'fn': total_fn,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'matches': total_matches,
        'total_detections': total_detections,
        'total_ground_truths': total_ground_truths
    }

def test_iou_thresholds():
    """测试不同IoU阈值"""
    print("🎯 测试data_15在不同IoU阈值下的性能")
    print("="*60)
    
    # 加载数据
    detection_file = "Output/newdata_detection_results_fixed2.json"
    annotation_path = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/labels"
    data_path = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images"
    class_json = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/class.json"
    
    if not Path(detection_file).exists():
        print(f"❌ 检测结果文件不存在: {detection_file}")
        return
    
    with open(detection_file, 'r', encoding='utf-8') as f:
        detection_data = json.load(f)
    
    ground_truth = load_yolo_ground_truth_with_natural_sort(
        annotation_path, class_json, data_path, 
        sample_ratio=0.05, target_sequences=['data_15']
    )
    
    # 获取data_15的数据
    data15_detections = [d for d in detection_data if d['sequence_id'] == 'data_15']
    data15_gt = ground_truth.get('data_15', [])
    
    print(f"📊 data_15检测结果: {len(data15_detections)} 个")
    print(f"📊 data_15真实标注: {len(data15_gt)} 个")
    
    if not data15_detections or not data15_gt:
        print("❌ 没有数据可测试")
        return
    
    # 测试不同IoU阈值
    iou_thresholds = [0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.5]
    
    print(f"\n📋 不同IoU阈值下的性能表现:")
    print(f"{'IoU阈值':<8} {'精确率':<8} {'召回率':<8} {'F1分数':<8} {'TP':<4} {'FP':<4} {'FN':<4} {'匹配数':<6}")
    print("-" * 70)
    
    best_f1 = 0
    best_threshold = 0
    results = []
    
    for threshold in iou_thresholds:
        result = evaluate_with_iou_threshold(data15_detections, data15_gt, threshold)
        results.append(result)
        
        print(f"{threshold:<8.2f} {result['precision']:<8.3f} {result['recall']:<8.3f} "
              f"{result['f1']:<8.3f} {result['tp']:<4} {result['fp']:<4} "
              f"{result['fn']:<4} {result['matches']:<6}")
        
        if result['f1'] > best_f1:
            best_f1 = result['f1']
            best_threshold = threshold
    
    print("-" * 70)
    print(f"🏆 最佳IoU阈值: {best_threshold:.2f} (F1分数: {best_f1:.3f})")
    
    # 分析IoU分布
    print(f"\n🔍 IoU分布分析:")
    
    # 计算所有检测-标注对的IoU
    all_ious = []
    for det in data15_detections:
        frame_gts = [gt for gt in data15_gt if gt.frame_id == det['frame_id']]
        for gt in frame_gts:
            if det['label'] == gt.label:
                iou = calculate_iou(det['bbox'], gt.bbox)
                all_ious.append(iou)
    
    if all_ious:
        all_ious.sort()
        print(f"  总IoU对数: {len(all_ious)}")
        print(f"  最小IoU: {min(all_ious):.3f}")
        print(f"  最大IoU: {max(all_ious):.3f}")
        print(f"  平均IoU: {sum(all_ious)/len(all_ious):.3f}")
        print(f"  中位数IoU: {all_ious[len(all_ious)//2]:.3f}")
        
        # 计算不同阈值下的覆盖率
        print(f"\n📊 IoU阈值覆盖率:")
        for threshold in [0.1, 0.15, 0.2, 0.25, 0.3]:
            coverage = sum(1 for iou in all_ious if iou >= threshold) / len(all_ious)
            print(f"  IoU >= {threshold:.2f}: {coverage:.1%}")
    
    # 推荐最佳阈值
    print(f"\n💡 推荐方案:")
    
    # 找到召回率>0.8的最高阈值
    high_recall_thresholds = [r for r in results if r['recall'] >= 0.8]
    if high_recall_thresholds:
        best_high_recall = max(high_recall_thresholds, key=lambda x: x['iou_threshold'])
        print(f"  1. 高召回率方案: IoU阈值 {best_high_recall['iou_threshold']:.2f} "
              f"(召回率: {best_high_recall['recall']:.3f}, 精确率: {best_high_recall['precision']:.3f})")
    
    # 找到F1分数最高的阈值
    print(f"  2. 最佳F1方案: IoU阈值 {best_threshold:.2f} "
          f"(F1: {best_f1:.3f})")
    
    # 找到精确率和召回率平衡的阈值
    balanced_results = [r for r in results if abs(r['precision'] - r['recall']) < 0.1]
    if balanced_results:
        best_balanced = max(balanced_results, key=lambda x: x['f1'])
        print(f"  3. 平衡方案: IoU阈值 {best_balanced['iou_threshold']:.2f} "
              f"(精确率: {best_balanced['precision']:.3f}, 召回率: {best_balanced['recall']:.3f})")

if __name__ == "__main__":
    test_iou_thresholds()
