#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标调试可视化工具
显示所有坐标系：原始检测、修正检测、原始标注、修正标注
"""

import os
import json
import sys
from PIL import Image, ImageDraw, ImageFont
from pathlib import Path

# 添加父目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from evaluate_finetuned_improved import load_yolo_ground_truth_with_natural_sort, load_class_map
from evaluate_finetuned_bbox_corrected import smart_resize, convert_bbox_to_model_coords

def visualize_coordinate_debug():
    """可视化坐标调试"""
    print("🔍 坐标调试可视化")
    print("="*60)
    
    # 文件路径
    detection_file = "Output/newdata_detection_results_fixed.json"
    annotation_path = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/labels"
    data_path = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images"
    class_json = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/class.json"
    
    # 加载数据
    with open(detection_file, 'r', encoding='utf-8') as f:
        detection_data = json.load(f)
    
    ground_truth = load_yolo_ground_truth_with_natural_sort(
        annotation_path, class_json, data_path, 
        sample_ratio=0.05, target_sequences=['data_15']
    )
    
    # 选择data_15的第一帧进行详细分析
    data15_detections = [d for d in detection_data if d['sequence_id'] == 'data_15']
    data15_gt = ground_truth.get('data_15', [])
    
    # 选择帧0进行分析
    frame_id = '0'
    frame_detections = [d for d in data15_detections if d['frame_id'] == frame_id]
    frame_gt = [gt for gt in data15_gt if gt.frame_id == frame_id]
    
    if not frame_detections or not frame_gt:
        print("❌ 没有找到帧0的数据")
        return
    
    det = frame_detections[0]
    gt = frame_gt[0]
    
    print(f"🔍 分析帧 {frame_id}:")
    print(f"📊 图像尺寸: {det['image_size']}")
    
    # 获取图像尺寸
    img_width, img_height = det['image_size']
    
    # 计算模型内部尺寸
    model_height, model_width = smart_resize(img_height, img_width)
    print(f"📊 模型内部尺寸: {model_width} × {model_height}")
    
    # 所有坐标系
    print(f"\n📋 坐标对比:")
    print(f"1. 检测结果 - 模型原始输出: {det['original_bbox']}")
    print(f"2. 检测结果 - 修正后坐标: {det['bbox']}")
    print(f"3. 真实标注 - 原始坐标: {gt.bbox}")
    
    # 将真实标注转换为模型坐标（这是评估时做的）
    gt_model_coords = convert_bbox_to_model_coords(gt.bbox, img_height, img_width)
    print(f"4. 真实标注 - 转换为模型坐标: {gt_model_coords}")
    
    # 计算各种IoU
    def calculate_iou(box1, box2):
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        inter_x1 = max(x1_1, x1_2)
        inter_y1 = max(y1_1, y1_2)
        inter_x2 = min(x2_1, x2_2)
        inter_y2 = min(y2_1, y2_2)
        
        if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
            return 0.0
        
        inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    print(f"\n📊 IoU计算:")
    
    # 1. 原始坐标系下的IoU（正确的）
    iou_original = calculate_iou(det['bbox'], gt.bbox)
    print(f"1. 修正检测 vs 原始标注: {iou_original:.3f} ✅ (正确的比较)")
    
    # 2. 模型坐标系下的IoU（评估时实际使用的）
    iou_model = calculate_iou(det['original_bbox'], gt_model_coords)
    print(f"2. 原始检测 vs 模型标注: {iou_model:.3f} ❌ (评估时使用的)")
    
    # 3. 错误的混合比较
    iou_mixed = calculate_iou(det['bbox'], gt_model_coords)
    print(f"3. 修正检测 vs 模型标注: {iou_mixed:.3f} ❌ (错误的混合)")
    
    print(f"\n🎯 问题分析:")
    if iou_original >= 0.3:
        print(f"✅ 在原始坐标系下，IoU = {iou_original:.3f} >= 0.3，应该匹配成功")
    else:
        print(f"❌ 在原始坐标系下，IoU = {iou_original:.3f} < 0.3，匹配失败")
    
    if iou_model >= 0.3:
        print(f"✅ 在模型坐标系下，IoU = {iou_model:.3f} >= 0.3，应该匹配成功")
    else:
        print(f"❌ 在模型坐标系下，IoU = {iou_model:.3f} < 0.3，匹配失败")
    
    print(f"\n💡 解决方案:")
    print(f"评估时应该使用原始坐标系进行比较，而不是转换标注到模型坐标系")
    
    # 可视化
    print(f"\n🎨 生成可视化图像...")
    
    # 加载图像
    img_path = Path(data_path) / "data_15" / "0.jpg"
    if not img_path.exists():
        print(f"❌ 图像文件不存在: {img_path}")
        return
    
    image = Image.open(img_path)
    if image.mode != 'RGB':
        image = image.convert('RGB')
    
    draw = ImageDraw.Draw(image)
    
    # 尝试加载字体
    try:
        font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 12)
    except:
        font = ImageFont.load_default()
    
    # 绘制不同的框
    # 1. 检测结果 - 修正后坐标（绿色实线）
    det_bbox = det['bbox']
    draw.rectangle(det_bbox, outline=(0, 255, 0), width=2)
    draw.text((det_bbox[0], det_bbox[1] - 15), f"DET修正:{iou_original:.3f}", fill=(0, 255, 0), font=font)
    
    # 2. 检测结果 - 原始坐标（蓝色实线）
    orig_det_bbox = det['original_bbox']
    # 需要转换回原始坐标系
    from evaluate_finetuned_bbox_corrected import convert_bbox_to_original_coords
    orig_det_in_orig_coords = convert_bbox_to_original_coords(orig_det_bbox, img_height, img_width)
    draw.rectangle(orig_det_in_orig_coords, outline=(0, 0, 255), width=2)
    draw.text((orig_det_in_orig_coords[0], orig_det_in_orig_coords[1] - 30), f"DET原始", fill=(0, 0, 255), font=font)
    
    # 3. 真实标注 - 原始坐标（红色虚线）
    gt_bbox = [int(x) for x in gt.bbox]
    draw_dashed_rectangle(draw, gt_bbox, (255, 0, 0), width=2)
    draw.text((gt_bbox[0], gt_bbox[1] - 45), f"GT原始", fill=(255, 0, 0), font=font)
    
    # 添加信息
    info_text = [
        f"帧{frame_id} - 坐标调试",
        f"图像尺寸: {img_width}×{img_height}",
        f"模型尺寸: {model_width}×{model_height}",
        f"正确IoU: {iou_original:.3f}",
        f"评估IoU: {iou_model:.3f}",
        "绿色=修正检测, 蓝色=原始检测, 红色虚线=标注"
    ]
    
    y_offset = 10
    for text in info_text:
        draw.text((10, y_offset), text, fill=(255, 255, 255), font=font)
        y_offset += 15
    
    # 保存图像
    output_path = "coordinate_debug_frame0.jpg"
    image.save(output_path, 'JPEG', quality=95)
    print(f"✅ 可视化图像已保存: {output_path}")

def draw_dashed_rectangle(draw, bbox, color, width=2, dash_length=5):
    """绘制虚线矩形"""
    x1, y1, x2, y2 = bbox
    
    # 上边
    for x in range(x1, x2, dash_length * 2):
        draw.line([x, y1, min(x + dash_length, x2), y1], fill=color, width=width)
    
    # 下边
    for x in range(x1, x2, dash_length * 2):
        draw.line([x, y2, min(x + dash_length, x2), y2], fill=color, width=width)
    
    # 左边
    for y in range(y1, y2, dash_length * 2):
        draw.line([x1, y, x1, min(y + dash_length, y2)], fill=color, width=width)
    
    # 右边
    for y in range(y1, y2, dash_length * 2):
        draw.line([x2, y, x2, min(y + dash_length, y2)], fill=color, width=width)

if __name__ == "__main__":
    visualize_coordinate_debug()
