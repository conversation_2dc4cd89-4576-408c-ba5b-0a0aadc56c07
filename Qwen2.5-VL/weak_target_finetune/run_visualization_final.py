#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终的可视化快速运行脚本
自动使用最新的检测结果进行可视化
"""

import os
import sys
import glob
from pathlib import Path

def find_latest_detection_results():
    """查找最新的检测结果文件（排除评估结果文件）"""
    output_dir = Path("Output")
    if not output_dir.exists():
        return None

    # 查找所有检测结果文件，但排除评估结果文件
    all_files = list(output_dir.glob("*detection_results*.json"))

    # 过滤掉评估结果文件
    result_files = []
    for file in all_files:
        # 排除包含 "evaluation" 的文件
        if "evaluation" not in file.name:
            result_files.append(file)

    if not result_files:
        print("❌ 未找到检测结果文件")
        print("📋 找到的文件:")
        for file in all_files:
            file_type = "评估结果" if "evaluation" in file.name else "检测结果"
            print(f"  - {file.name} ({file_type})")
        return None

    # 返回最新修改的检测结果文件
    latest_file = max(result_files, key=lambda x: x.stat().st_mtime)
    return str(latest_file)

def main():
    """主函数"""
    print("🎨 检测结果可视化工具")
    print("="*50)
    
    # 查找最新的检测结果文件
    detection_results = find_latest_detection_results()
    
    if detection_results is None:
        print("❌ 未找到检测结果文件")
        print("请先运行 evaluate_finetuned_improved.py 生成检测结果")
        return
    
    print(f"📁 使用检测结果文件: {detection_results}")
    
    # 设置默认参数
    data_paths = [
        "/home/<USER>/Qwen/Qwen2.5-VL/new_processed_data",
        "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images"
    ]
    
    # 查找存在的数据路径
    data_path = None
    for path in data_paths:
        if os.path.exists(path):
            data_path = path
            break
    
    if data_path is None:
        print("❌ 未找到图像数据目录")
        print("请检查以下路径是否存在:")
        for path in data_paths:
            print(f"  - {path}")
        return
    
    print(f"📁 使用图像数据目录: {data_path}")
    
    # 设置输出目录
    output_dir = "visual_result"
    print(f"📁 输出目录: {output_dir}")
    
    # 构建命令 - 使用简化版本的可视化脚本
    cmd = f'python visualize_simple.py --detection_results "{detection_results}" --data_path "{data_path}" --output_dir "{output_dir}"'
    
    print(f"\n🚀 执行命令:")
    print(f"   {cmd}")
    print("\n" + "="*50)
    
    # 执行命令
    result = os.system(cmd)
    
    if result == 0:
        print("\n🎉 可视化完成!")
        print(f"📁 结果保存在: {output_dir}/")
        print("📋 目录结构:")

        # 显示生成的目录结构
        visual_dir = Path(output_dir)
        if visual_dir.exists():
            total_frames = 0
            for seq_dir in sorted(visual_dir.iterdir()):
                if seq_dir.is_dir():
                    frame_count = len(list(seq_dir.glob("*.jpg")))
                    total_frames += frame_count
                    print(f"  📂 {seq_dir.name}/ ({frame_count} 帧)")
            print(f"\n📊 总计: {total_frames} 帧可视化图像")

        print(f"\n💡 您可以使用图像查看器打开 {output_dir}/ 目录查看可视化结果")
        print(f"🎯 每个检测框都标注了类别、置信度和时序得分")
    else:
        print("\n❌ 可视化失败，请检查错误信息")

if __name__ == "__main__":
    main()
