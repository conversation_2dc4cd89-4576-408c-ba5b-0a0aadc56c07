#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版可视化工具 - 同时显示检测结果和真实标注
用于调试评估问题，直观对比检测框和标注框
"""

import os
import json
from PIL import Image, ImageDraw, ImageFont
from pathlib import Path
from typing import List, Dict, Tuple
import argparse
from collections import defaultdict
import sys

# 添加父目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入标注加载函数
from evaluate_finetuned_improved import load_yolo_ground_truth_with_natural_sort, load_class_map

class GroundTruthVisualizer:
    """真实标注和检测结果对比可视化器"""
    
    def __init__(self, output_dir: str = "visual_result_with_gt"):
        """初始化可视化器"""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 定义颜色：检测框用实线，标注框用虚线
        self.detection_color = (0, 255, 0)    # 绿色 - 检测框
        self.groundtruth_color = (255, 0, 0)  # 红色 - 真实标注框
        self.matched_color = (0, 255, 255)    # 青色 - 匹配的框
        
        # 尝试加载字体
        try:
            self.font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 14)
            self.small_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 12)
        except:
            self.font = ImageFont.load_default()
            self.small_font = ImageFont.load_default()
    
    def draw_bbox_with_label(self, draw, bbox, label, color, style="solid", prefix=""):
        """绘制带标签的边界框"""
        x1, y1, x2, y2 = [int(coord) for coord in bbox[:4]]
        
        # 绘制边界框
        if style == "dashed":
            # 绘制虚线框（模拟）
            self.draw_dashed_rectangle(draw, [x1, y1, x2, y2], color, width=2)
        else:
            # 绘制实线框
            draw.rectangle([x1, y1, x2, y2], outline=color, width=2)
        
        # 准备标签文本
        text = f"{prefix}{label}" if prefix else label
        
        # 计算文本尺寸
        bbox_text = draw.textbbox((0, 0), text, font=self.small_font)
        text_width = bbox_text[2] - bbox_text[0]
        text_height = bbox_text[3] - bbox_text[1]
        
        # 绘制文本背景
        draw.rectangle([x1, y1 - text_height - 4, x1 + text_width + 4, y1], fill=color)
        
        # 绘制文本
        draw.text((x1 + 2, y1 - text_height - 2), text, fill=(255, 255, 255), font=self.small_font)
    
    def draw_dashed_rectangle(self, draw, bbox, color, width=2, dash_length=5):
        """绘制虚线矩形"""
        x1, y1, x2, y2 = bbox
        
        # 上边
        for x in range(x1, x2, dash_length * 2):
            draw.line([x, y1, min(x + dash_length, x2), y1], fill=color, width=width)
        
        # 下边
        for x in range(x1, x2, dash_length * 2):
            draw.line([x, y2, min(x + dash_length, x2), y2], fill=color, width=width)
        
        # 左边
        for y in range(y1, y2, dash_length * 2):
            draw.line([x1, y, x1, min(y + dash_length, y2)], fill=color, width=width)
        
        # 右边
        for y in range(y1, y2, dash_length * 2):
            draw.line([x2, y, x2, min(y + dash_length, y2)], fill=color, width=width)
    
    def calculate_iou(self, box1, box2):
        """计算两个框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # 计算交集
        inter_x1 = max(x1_1, x1_2)
        inter_y1 = max(y1_1, y1_2)
        inter_x2 = min(x2_1, x2_2)
        inter_y2 = min(y2_1, y2_2)
        
        if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
            return 0.0
        
        inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def visualize_frame_with_gt(self, image_path: str, detections: List[Dict], 
                               ground_truths: List[Dict], output_path: str, 
                               iou_threshold: float = 0.3) -> Dict:
        """可视化单帧的检测结果和真实标注"""
        try:
            # 读取图像
            if not os.path.exists(image_path):
                print(f"警告: 图像文件不存在: {image_path}")
                return {"success": False, "error": "image_not_found"}
                
            image = Image.open(image_path)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            draw = ImageDraw.Draw(image)
            
            # 统计信息
            stats = {
                "detections": len(detections),
                "ground_truths": len(ground_truths),
                "matches": 0,
                "detection_boxes": [],
                "gt_boxes": [],
                "matched_pairs": []
            }
            
            # 转换检测结果为bbox格式
            det_boxes = []
            for det in detections:
                bbox = det['bbox']
                det_boxes.append({
                    'bbox': bbox,
                    'label': det['label'],
                    'confidence': det.get('confidence', 0.0)
                })
                stats["detection_boxes"].append(bbox)
            
            # 转换真实标注为bbox格式
            gt_boxes = []
            for gt in ground_truths:
                bbox = gt['bbox']
                gt_boxes.append({
                    'bbox': bbox,
                    'label': gt['label']
                })
                stats["gt_boxes"].append(bbox)
            
            # 匹配检测框和真实框
            matched_dets = set()
            matched_gts = set()
            
            for i, det_box in enumerate(det_boxes):
                best_iou = 0
                best_gt_idx = -1
                
                for j, gt_box in enumerate(gt_boxes):
                    if j in matched_gts:
                        continue
                    
                    # 只匹配相同类别
                    if det_box['label'] != gt_box['label']:
                        continue
                    
                    iou = self.calculate_iou(det_box['bbox'], gt_box['bbox'])
                    if iou > best_iou:
                        best_iou = iou
                        best_gt_idx = j
                
                if best_iou >= iou_threshold and best_gt_idx not in matched_gts:
                    matched_dets.add(i)
                    matched_gts.add(best_gt_idx)
                    stats["matched_pairs"].append({
                        "det_idx": i,
                        "gt_idx": best_gt_idx,
                        "iou": best_iou
                    })
                    stats["matches"] += 1
            
            # 绘制真实标注框（红色虚线）
            for j, gt_box in enumerate(gt_boxes):
                color = self.matched_color if j in matched_gts else self.groundtruth_color
                self.draw_bbox_with_label(
                    draw, gt_box['bbox'], gt_box['label'], 
                    color, style="dashed", prefix="GT:"
                )
            
            # 绘制检测框（绿色实线）
            for i, det_box in enumerate(det_boxes):
                color = self.matched_color if i in matched_dets else self.detection_color
                confidence = det_box['confidence']
                label_text = f"{det_box['label']}:{confidence:.2f}"
                self.draw_bbox_with_label(
                    draw, det_box['bbox'], label_text, 
                    color, style="solid", prefix="DET:"
                )
            
            # 添加统计信息到图像上
            info_text = [
                f"检测: {len(detections)} | 标注: {len(ground_truths)} | 匹配: {stats['matches']}",
                f"IoU阈值: {iou_threshold}",
                "绿色实线=检测框, 红色虚线=标注框, 青色=匹配"
            ]
            
            y_offset = 10
            for text in info_text:
                draw.text((10, y_offset), text, fill=(255, 255, 255), font=self.font)
                y_offset += 20
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 保存图像
            image.save(output_path, 'JPEG', quality=95)
            stats["success"] = True
            return stats
                
        except Exception as e:
            print(f"错误: 可视化帧时出错: {e}")
            return {"success": False, "error": str(e)}
    
    def find_image_file(self, data_path: str, seq_id: str, frame_id: str) -> str:
        """查找对应的图像文件"""
        seq_dir = Path(data_path) / seq_id
        if not seq_dir.exists():
            return None
        
        # 尝试不同的文件扩展名
        extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        
        # 首先尝试直接匹配frame_id
        for ext in extensions:
            candidate = seq_dir / f"{frame_id}{ext}"
            if candidate.exists():
                return str(candidate)
        
        # 如果直接匹配失败，尝试按索引查找
        try:
            frame_idx = int(frame_id)
            
            # 获取所有图像文件并排序
            image_files = []
            for ext in extensions:
                image_files.extend(seq_dir.glob(f'*{ext}'))
            
            if not image_files:
                return None
            
            # 使用自然排序
            def natural_sort_key(filename: str):
                import re
                numbers = re.findall(r'\d+', filename)
                if numbers:
                    return [int(numbers[-1])]
                else:
                    return [filename.lower()]
            
            image_files.sort(key=lambda x: natural_sort_key(x.name))
            
            # 按索引返回对应文件
            if frame_idx < len(image_files):
                return str(image_files[frame_idx])
                
        except ValueError:
            pass
        
        return None

    def visualize_with_groundtruth(self, detection_results_path: str, annotation_path: str,
                                  data_path: str, class_json_path: str, sequences: List[str] = None,
                                  sample_ratio: float = 0.05, iou_threshold: float = 0.3) -> Dict:
        """可视化检测结果和真实标注的对比"""
        print("🔍 开始对比可视化检测结果和真实标注...")

        # 加载检测结果
        try:
            with open(detection_results_path, 'r', encoding='utf-8') as f:
                detection_data = json.load(f)
        except Exception as e:
            print(f"错误: 无法加载检测结果文件: {e}")
            return {}

        # 加载真实标注
        try:
            ground_truth = load_yolo_ground_truth_with_natural_sort(
                annotation_path, class_json_path, data_path, sample_ratio, sequences
            )
        except Exception as e:
            print(f"错误: 无法加载真实标注: {e}")
            return {}

        # 按序列和帧分组检测结果
        detections_by_seq_frame = defaultdict(lambda: defaultdict(list))
        for detection in detection_data:
            seq_id = detection['sequence_id']
            frame_id = detection['frame_id']

            if sequences and seq_id not in sequences:
                continue

            detections_by_seq_frame[seq_id][frame_id].append(detection)

        # 按序列和帧分组真实标注
        gt_by_seq_frame = defaultdict(lambda: defaultdict(list))
        for seq_id, gt_list in ground_truth.items():
            for gt in gt_list:
                frame_id = gt.frame_id
                gt_dict = {
                    'bbox': gt.bbox,
                    'label': gt.label,
                    'frame_id': frame_id,
                    'sequence_id': seq_id
                }
                gt_by_seq_frame[seq_id][frame_id].append(gt_dict)

        # 统计信息
        stats = {
            'total_sequences': 0,
            'total_frames': 0,
            'successful_visualizations': 0,
            'failed_visualizations': 0,
            'sequence_stats': {},
            'debug_info': {
                'sequences_with_detections': list(detections_by_seq_frame.keys()),
                'sequences_with_gt': list(gt_by_seq_frame.keys()),
                'detection_count': len(detection_data),
                'gt_sequences_count': {seq: len(gts) for seq, gts in ground_truth.items()}
            }
        }

        # 获取所有需要处理的序列
        all_sequences = set(detections_by_seq_frame.keys()) | set(gt_by_seq_frame.keys())
        if sequences:
            all_sequences = all_sequences & set(sequences)

        stats['total_sequences'] = len(all_sequences)

        print(f"📊 处理统计:")
        print(f"  检测结果包含序列: {list(detections_by_seq_frame.keys())}")
        print(f"  标注数据包含序列: {list(gt_by_seq_frame.keys())}")
        print(f"  将要处理的序列: {sorted(all_sequences)}")

        return stats
