#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析data_15的IoU分布，找出评估为0的真正原因
"""

import json

def calculate_iou(box1, box2):
    """计算IoU"""
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2
    
    inter_x1 = max(x1_1, x1_2)
    inter_y1 = max(y1_1, y1_2)
    inter_x2 = min(x2_1, x2_2)
    inter_y2 = min(y2_1, y2_2)
    
    if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
        return 0.0
    
    inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = area1 + area2 - inter_area
    
    return inter_area / union_area if union_area > 0 else 0.0

def main():
    """主函数"""
    print("🔍 分析data_15的IoU分布")
    print("="*50)
    
    # 简单的IoU测试
    # 检测结果示例坐标
    det_coords = [253, 252, 260, 261]  # 帧0的检测结果
    gt_coords = [254.0, 254.0, 258.0, 258.0]  # 帧0的真实标注
    
    iou = calculate_iou(det_coords, gt_coords)
    print(f"📊 帧0 IoU计算:")
    print(f"  检测框: {det_coords}")
    print(f"  标注框: {gt_coords}")
    print(f"  IoU: {iou:.3f}")
    
    # 分析框的重叠情况
    det_area = (det_coords[2] - det_coords[0]) * (det_coords[3] - det_coords[1])
    gt_area = (gt_coords[2] - gt_coords[0]) * (gt_coords[3] - gt_coords[1])
    
    print(f"\n📐 框的尺寸分析:")
    print(f"  检测框尺寸: {det_coords[2] - det_coords[0]} × {det_coords[3] - det_coords[1]} = {det_area} 像素²")
    print(f"  标注框尺寸: {gt_coords[2] - gt_coords[0]} × {gt_coords[3] - gt_coords[1]} = {gt_area} 像素²")
    
    # 计算中心点距离
    det_center = [(det_coords[0] + det_coords[2])/2, (det_coords[1] + det_coords[3])/2]
    gt_center = [(gt_coords[0] + gt_coords[2])/2, (gt_coords[1] + gt_coords[3])/2]
    center_dist = ((det_center[0] - gt_center[0])**2 + (det_center[1] - gt_center[1])**2)**0.5
    
    print(f"\n📍 中心点分析:")
    print(f"  检测框中心: ({det_center[0]:.1f}, {det_center[1]:.1f})")
    print(f"  标注框中心: ({gt_center[0]:.1f}, {gt_center[1]:.1f})")
    print(f"  中心点距离: {center_dist:.1f} 像素")
    
    # 分析不同IoU阈值下的匹配情况
    print(f"\n🎯 不同IoU阈值下的匹配情况:")
    thresholds = [0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.5]
    for threshold in thresholds:
        match = "✅ 匹配" if iou >= threshold else "❌ 不匹配"
        print(f"  IoU >= {threshold}: {match}")
    
    print(f"\n💡 结论:")
    if iou < 0.3:
        print(f"❌ 当前IoU ({iou:.3f}) 低于标准阈值0.3，这就是评估为0的原因")
        print(f"🔧 可能的解决方案:")
        print(f"  1. 降低IoU阈值到0.2或0.25")
        print(f"  2. 改进模型的检测精度")
        print(f"  3. 检查标注数据的准确性")
    else:
        print(f"✅ IoU ({iou:.3f}) 满足阈值要求，应该匹配成功")
    
    # 测试其他几个坐标
    print(f"\n🔍 测试其他帧的坐标:")
    test_cases = [
        ([257, 252, 264, 261], [254.2, 253.91, 258.2, 257.91], "帧15"),
        ([254, 253, 264, 262], [254.39, 253.83, 258.39, 257.83], "帧29"),
        ([254, 253, 262, 261], [254.52, 253.78, 258.52, 257.78], "帧39")
    ]
    
    for det_box, gt_box, frame_name in test_cases:
        iou = calculate_iou(det_box, gt_box)
        match = "✅" if iou >= 0.3 else "❌"
        print(f"  {frame_name}: IoU = {iou:.3f} {match}")

if __name__ == "__main__":
    main()
