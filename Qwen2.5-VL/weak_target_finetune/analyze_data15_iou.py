#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析data_15的IoU分布，找出最佳阈值
"""

import json
import sys
from pathlib import Path

# 添加父目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from evaluate_finetuned_bbox_corrected import load_yolo_ground_truth_with_bbox_correction, load_class_map

def calculate_iou(box1, box2):
    """计算IoU"""
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2
    
    inter_x1 = max(x1_1, x1_2)
    inter_y1 = max(y1_1, y1_2)
    inter_x2 = min(x2_1, x2_2)
    inter_y2 = min(y2_1, y2_2)
    
    if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
        return 0.0
    
    inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = area1 + area2 - inter_area
    
    return inter_area / union_area if union_area > 0 else 0.0

def analyze_data15_iou():
    """分析data_15的IoU分布"""
    print("🔍 详细分析data_15的IoU分布")
    print("="*60)
    
    # 加载数据
    detection_file = "Output/newdata_detection_results_fixed2.json"
    annotation_path = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/labels"
    data_path = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images"
    class_json = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/class.json"
    
    with open(detection_file, 'r', encoding='utf-8') as f:
        detection_data = json.load(f)
    
    ground_truth = load_yolo_ground_truth_with_natural_sort(
        annotation_path, class_json, data_path, 
        sample_ratio=0.05, target_sequences=['data_15']
    )
    
    # 获取data_15的数据
    data15_detections = [d for d in detection_data if d['sequence_id'] == 'data_15']
    data15_gt = ground_truth.get('data_15', [])
    
    print(f"📊 data_15检测结果: {len(data15_detections)} 个")
    print(f"📊 data_15真实标注: {len(data15_gt)} 个")
    
    # 按帧分组
    det_by_frame = {}
    gt_by_frame = {}
    
    for det in data15_detections:
        frame_id = det['frame_id']
        if frame_id not in det_by_frame:
            det_by_frame[frame_id] = []
        det_by_frame[frame_id].append(det)
    
    for gt in data15_gt:
        frame_id = gt.frame_id
        if frame_id not in gt_by_frame:
            gt_by_frame[frame_id] = []
        gt_by_frame[frame_id].append(gt)
    
    # 计算所有IoU
    all_ious = []
    frame_stats = []
    
    for frame_id in sorted(det_by_frame.keys(), key=lambda x: int(x)):
        if frame_id not in gt_by_frame:
            continue
            
        frame_dets = det_by_frame[frame_id]
        frame_gts = gt_by_frame[frame_id]
        
        frame_ious = []
        for det in frame_dets:
            for gt in frame_gts:
                if det['label'] == gt.label:  # 只计算相同类别的IoU
                    iou = calculate_iou(det['bbox'], gt.bbox)
                    frame_ious.append(iou)
                    all_ious.append(iou)
        
        if frame_ious:
            max_iou = max(frame_ious)
            avg_iou = sum(frame_ious) / len(frame_ious)
            frame_stats.append({
                'frame_id': frame_id,
                'max_iou': max_iou,
                'avg_iou': avg_iou,
                'det_count': len(frame_dets),
                'gt_count': len(frame_gts)
            })
    
    # 统计分析
    if all_ious:
        all_ious.sort()
        n = len(all_ious)

        print(f"\n📊 IoU统计分析:")
        print(f"  总IoU数量: {n}")
        print(f"  平均IoU: {sum(all_ious)/n:.3f}")
        print(f"  中位数IoU: {all_ious[n//2]:.3f}")
        print(f"  最大IoU: {max(all_ious):.3f}")
        print(f"  最小IoU: {min(all_ious):.3f}")

        # 分位数分析
        percentiles = [10, 25, 50, 75, 90, 95, 99]
        print(f"\n📈 IoU分位数分析:")
        for p in percentiles:
            idx = int(n * p / 100)
            if idx >= n:
                idx = n - 1
            value = all_ious[idx]
            print(f"  {p}%分位数: {value:.3f}")

        # 不同阈值下的匹配率
        print(f"\n🎯 不同IoU阈值下的匹配率:")
        thresholds = [0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.5]
        for threshold in thresholds:
            match_count = sum(1 for iou in all_ious if iou >= threshold)
            match_rate = match_count / n * 100
            print(f"  IoU >= {threshold}: {match_count}/{n} ({match_rate:.1f}%)")

        # 找出最佳阈值
        print(f"\n💡 推荐阈值分析:")

        # 基于50%匹配率的阈值
        threshold_50 = all_ious[n//2]
        print(f"  50%匹配率阈值: {threshold_50:.3f}")

        # 基于75%匹配率的阈值
        threshold_75 = all_ious[int(n*0.25)]
        print(f"  75%匹配率阈值: {threshold_75:.3f}")

        # 基于90%匹配率的阈值
        threshold_90 = all_ious[int(n*0.1)]
        print(f"  90%匹配率阈值: {threshold_90:.3f}")
        
        # 显示前10帧的详细情况
        print(f"\n📋 前10帧详细IoU情况:")
        for i, stat in enumerate(frame_stats[:10]):
            print(f"  帧{stat['frame_id']}: 最大IoU={stat['max_iou']:.3f}, "
                  f"平均IoU={stat['avg_iou']:.3f}, "
                  f"检测{stat['det_count']}/标注{stat['gt_count']}")
        
        # 建议
        print(f"\n🚀 提升建议:")
        
        if threshold_90 < 0.3:
            print(f"  1. 降低IoU阈值到 {threshold_90:.2f} 可获得90%匹配率")
        
        if threshold_75 < 0.3:
            print(f"  2. 降低IoU阈值到 {threshold_75:.2f} 可获得75%匹配率")
        
        avg_iou = sum(all_ious) / len(all_ious)
        if avg_iou < 0.3:
            print(f"  3. 当前平均IoU ({avg_iou:.3f}) 低于标准阈值0.3")
            print(f"     这主要是检测框尺寸问题，建议:")
            print(f"     - 改进模型的边界框回归")
            print(f"     - 优化训练数据中的标注质量")
            print(f"     - 调整损失函数权重")
        
        # 生成测试命令
        recommended_threshold = max(0.1, min(0.25, threshold_75))
        print(f"\n🔧 推荐测试命令:")
        print(f"CUDA_VISIBLE_DEVICES=0 python evaluate_finetuned_bbox_corrected.py \\")
        print(f"    --base_model_path \"/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct\" \\")
        print(f"    --data_path \"/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images\" \\")
        print(f"    --annotation_path \"/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/labels\" \\")
        print(f"    --output_path \"./Output/data15_improved_results.json\" \\")
        print(f"    --class_json \"/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/class.json\" \\")
        print(f"    --sample_ratio 0.05 \\")
        print(f"    --iou_threshold {recommended_threshold:.2f} \\")
        print(f"    --sequences data_15")

def main():
    """主函数"""
    analyze_data15_iou()

if __name__ == "__main__":
    main()
