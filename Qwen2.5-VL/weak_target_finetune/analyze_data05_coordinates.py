#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析data_05的坐标问题
"""

import json
import sys
from pathlib import Path

# 添加父目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from evaluate_finetuned_improved import load_yolo_ground_truth_with_natural_sort, load_class_map
from evaluate_finetuned_bbox_corrected import smart_resize, convert_bbox_to_model_coords, convert_bbox_to_original_coords

def analyze_data05_coordinates():
    """分析data_05的坐标问题"""
    print("🔍 详细分析data_05坐标问题")
    print("="*60)
    
    # 加载数据
    detection_file = "Output/newdata_detection_results_fixed.json"
    annotation_path = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/labels"
    data_path = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images"
    class_json = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/class.json"
    
    with open(detection_file, 'r', encoding='utf-8') as f:
        detection_data = json.load(f)
    
    ground_truth = load_yolo_ground_truth_with_natural_sort(
        annotation_path, class_json, data_path, 
        sample_ratio=0.05, target_sequences=['data_05']
    )
    
    # 获取data_05的数据
    data05_detections = [d for d in detection_data if d['sequence_id'] == 'data_05']
    data05_gt = ground_truth.get('data_05', [])
    
    print(f"📊 data_05检测结果: {len(data05_detections)} 个")
    print(f"📊 data_05真实标注: {len(data05_gt)} 个")
    
    # 分析帧0的详细情况
    frame_id = '0'
    frame_detections = [d for d in data05_detections if d['frame_id'] == frame_id]
    frame_gt = [gt for gt in data05_gt if gt.frame_id == frame_id]
    
    print(f"\n🔍 详细分析帧 {frame_id}:")
    print(f"📊 检测结果数: {len(frame_detections)}")
    print(f"📊 真实标注数: {len(frame_gt)}")
    
    if not frame_detections or not frame_gt:
        print("❌ 没有数据可分析")
        return
    
    # 获取图像尺寸
    img_width, img_height = frame_detections[0]['image_size']
    print(f"📊 图像尺寸: {img_width} × {img_height}")
    
    # 计算模型内部尺寸
    model_height, model_width = smart_resize(img_height, img_width)
    print(f"📊 模型内部尺寸: {model_width} × {model_height}")
    
    print(f"\n📋 检测结果详情:")
    for i, det in enumerate(frame_detections):
        print(f"  检测{i+1}:")
        print(f"    原始模型输出: {det['original_bbox']}")
        print(f"    修正后坐标: {det['bbox']}")
        print(f"    置信度: {det['confidence']}")
        print(f"    是否修正: {det['coordinate_corrected']}")
    
    print(f"\n📋 真实标注详情:")
    for i, gt in enumerate(frame_gt):
        print(f"  标注{i+1}:")
        print(f"    原始坐标: {gt.bbox}")
        
        # 转换为模型坐标
        gt_model_coords = convert_bbox_to_model_coords(gt.bbox, img_height, img_width)
        print(f"    转换为模型坐标: {gt_model_coords}")
    
    # 分析坐标转换的正确性
    print(f"\n🔧 坐标转换验证:")
    for i, det in enumerate(frame_detections):
        print(f"\n  检测{i+1}坐标转换验证:")
        
        # 1. 原始模型输出
        original_model_bbox = det['original_bbox']
        print(f"    1. 模型原始输出: {original_model_bbox}")
        
        # 2. 转换为原始图像坐标
        converted_original = convert_bbox_to_original_coords(original_model_bbox, img_height, img_width)
        print(f"    2. 转换为原始坐标: {converted_original}")
        
        # 3. 实际保存的修正坐标
        saved_corrected = det['bbox']
        print(f"    3. 保存的修正坐标: {saved_corrected}")
        
        # 4. 检查是否一致
        diff = [abs(a - b) for a, b in zip(converted_original, saved_corrected)]
        max_diff = max(diff)
        print(f"    4. 转换差异: {diff} (最大: {max_diff:.1f})")
        
        if max_diff < 1:
            print(f"    ✅ 坐标转换正确")
        else:
            print(f"    ❌ 坐标转换可能有问题")
    
    # 计算IoU
    def calculate_iou(box1, box2):
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        inter_x1 = max(x1_1, x1_2)
        inter_y1 = max(y1_1, y1_2)
        inter_x2 = min(x2_1, x2_2)
        inter_y2 = min(y2_1, y2_2)
        
        if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
            return 0.0
        
        inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    print(f"\n📊 IoU分析:")
    for i, det in enumerate(frame_detections):
        for j, gt in enumerate(frame_gt):
            # 在原始坐标系下计算IoU
            iou = calculate_iou(det['bbox'], gt.bbox)
            print(f"  检测{i+1} vs 标注{j+1}: IoU = {iou:.3f}")
            
            # 分析位置关系
            det_center = [(det['bbox'][0] + det['bbox'][2])/2, (det['bbox'][1] + det['bbox'][3])/2]
            gt_center = [(gt.bbox[0] + gt.bbox[2])/2, (gt.bbox[1] + gt.bbox[3])/2]
            center_dist = ((det_center[0] - gt_center[0])**2 + (det_center[1] - gt_center[1])**2)**0.5
            
            print(f"    检测中心: ({det_center[0]:.1f}, {det_center[1]:.1f})")
            print(f"    标注中心: ({gt_center[0]:.1f}, {gt_center[1]:.1f})")
            print(f"    中心距离: {center_dist:.1f} 像素")
    
    # 检查是否存在目标匹配错误
    print(f"\n🎯 目标匹配分析:")
    
    # 为每个检测找最佳匹配
    for i, det in enumerate(frame_detections):
        best_iou = 0
        best_gt_idx = -1
        
        for j, gt in enumerate(frame_gt):
            iou = calculate_iou(det['bbox'], gt.bbox)
            if iou > best_iou:
                best_iou = iou
                best_gt_idx = j
        
        if best_iou > 0:
            print(f"  检测{i+1} 最佳匹配: 标注{best_gt_idx+1}, IoU = {best_iou:.3f}")
        else:
            print(f"  检测{i+1} 没有匹配的标注 (所有IoU = 0)")
    
    print(f"\n💡 问题诊断:")
    
    # 检查检测框和标注框的尺寸
    det_sizes = []
    gt_sizes = []
    
    for det in frame_detections:
        w = det['bbox'][2] - det['bbox'][0]
        h = det['bbox'][3] - det['bbox'][1]
        det_sizes.append((w, h))
    
    for gt in frame_gt:
        w = gt.bbox[2] - gt.bbox[0]
        h = gt.bbox[3] - gt.bbox[1]
        gt_sizes.append((w, h))
    
    print(f"  检测框尺寸: {det_sizes}")
    print(f"  标注框尺寸: {[(round(w,1), round(h,1)) for w, h in gt_sizes]}")
    
    # 分析可能的问题
    avg_det_size = sum(w*h for w, h in det_sizes) / len(det_sizes)
    avg_gt_size = sum(w*h for w, h in gt_sizes) / len(gt_sizes)
    
    print(f"  平均检测框面积: {avg_det_size:.1f}")
    print(f"  平均标注框面积: {avg_gt_size:.1f}")
    
    if avg_det_size > avg_gt_size * 2:
        print(f"  ⚠️ 检测框明显比标注框大")
    elif avg_det_size < avg_gt_size * 0.5:
        print(f"  ⚠️ 检测框明显比标注框小")
    
    # 检查位置偏移
    all_center_dists = []
    for i, det in enumerate(frame_detections):
        for j, gt in enumerate(frame_gt):
            det_center = [(det['bbox'][0] + det['bbox'][2])/2, (det['bbox'][1] + det['bbox'][3])/2]
            gt_center = [(gt.bbox[0] + gt.bbox[2])/2, (gt.bbox[1] + gt.bbox[3])/2]
            center_dist = ((det_center[0] - gt_center[0])**2 + (det_center[1] + gt_center[1])**2)**0.5
            all_center_dists.append(center_dist)
    
    min_center_dist = min(all_center_dists)
    print(f"  最小中心距离: {min_center_dist:.1f} 像素")
    
    if min_center_dist > 20:
        print(f"  ❌ 检测位置偏移严重 (>20像素)")
    elif min_center_dist > 10:
        print(f"  ⚠️ 检测位置有偏移 (>10像素)")
    else:
        print(f"  ✅ 检测位置基本准确 (<10像素)")

if __name__ == "__main__":
    analyze_data05_coordinates()
