#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可视化功能
创建一些示例检测结果并测试可视化
"""

import json
import os
import tempfile
from pathlib import Path

def create_test_detection_results():
    """创建测试用的检测结果"""
    test_results = [
        {
            "sequence_id": "data03",
            "frame_id": "0",
            "bbox": [100, 100, 200, 200],
            "label": "drone",
            "confidence": 0.85,
            "temporal_score": 0.8
        },
        {
            "sequence_id": "data03",
            "frame_id": "0",
            "bbox": [300, 150, 400, 250],
            "label": "car",
            "confidence": 0.75,
            "temporal_score": 0.7
        },
        {
            "sequence_id": "data03",
            "frame_id": "1",
            "bbox": [105, 105, 205, 205],
            "label": "drone",
            "confidence": 0.88,
            "temporal_score": 0.85
        },
        {
            "sequence_id": "data05",
            "frame_id": "0",
            "bbox": [50, 50, 150, 150],
            "label": "ship",
            "confidence": 0.92,
            "temporal_score": 0.9
        }
    ]
    
    return test_results

def test_visualization():
    """测试可视化功能"""
    print("🧪 测试可视化功能")
    print("="*40)
    
    # 创建测试检测结果
    test_results = create_test_detection_results()
    
    # 保存到临时文件
    test_file = "test_detection_results.json"
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_results, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 创建测试检测结果文件: {test_file}")
    print(f"📊 包含 {len(test_results)} 个检测结果")
    
    # 显示测试结果内容
    print("\n📋 测试检测结果:")
    for i, result in enumerate(test_results):
        print(f"  {i+1}. 序列:{result['sequence_id']}, 帧:{result['frame_id']}, "
              f"类别:{result['label']}, 置信度:{result['confidence']:.2f}")
    
    print(f"\n💡 要测试可视化，请运行:")
    print(f"   python visualize_detection_results.py \\")
    print(f"     --detection_results {test_file} \\")
    print(f"     --data_path /path/to/your/images \\")
    print(f"     --output_dir test_visual_result \\")
    print(f"     --verbose")
    
    print(f"\n🗑️ 测试完成后可删除: {test_file}")

if __name__ == "__main__":
    test_visualization()
