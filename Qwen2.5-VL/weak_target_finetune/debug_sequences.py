#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试序列问题 - 检查指定的序列是否存在
"""

import os
from pathlib import Path

def debug_sequences():
    """调试序列存在性"""
    
    # 使用您的实际路径
    data_path = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images"
    annotation_path = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/labels"
    specified_sequences = ['data_02', 'data_05', 'data_06', 'data_14', 'data_15']
    
    print("=== 调试序列存在性 ===")
    print(f"数据路径: {data_path}")
    print(f"标注路径: {annotation_path}")
    print(f"指定序列: {specified_sequences}")
    
    # 检查数据路径中的所有序列
    data_path_obj = Path(data_path)
    if data_path_obj.exists():
        all_sequences = [d.name for d in data_path_obj.iterdir() if d.is_dir()]
        all_sequences.sort()
        print(f"\n数据路径中的所有序列: {all_sequences}")
        
        # 检查指定序列是否存在
        existing_sequences = []
        missing_sequences = []
        for seq in specified_sequences:
            seq_path = data_path_obj / seq
            if seq_path.exists() and seq_path.is_dir():
                existing_sequences.append(seq)
                # 检查序列中的文件数量
                image_files = []
                for ext in ['*.bmp', '*.jpg', '*.jpeg', '*.png']:
                    image_files.extend(seq_path.glob(ext))
                print(f"  {seq}: 存在，包含 {len(image_files)} 个图像文件")
            else:
                missing_sequences.append(seq)
                print(f"  {seq}: 不存在")
        
        print(f"\n存在的序列: {existing_sequences}")
        print(f"缺失的序列: {missing_sequences}")
    else:
        print(f"错误: 数据路径不存在: {data_path}")
        return
    
    # 检查标注路径中的序列
    annotation_path_obj = Path(annotation_path)
    if annotation_path_obj.exists():
        all_label_sequences = [d.name for d in annotation_path_obj.iterdir() if d.is_dir()]
        all_label_sequences.sort()
        print(f"\n标注路径中的所有序列: {all_label_sequences}")
        
        # 检查指定序列的标注是否存在
        for seq in specified_sequences:
            seq_label_path = annotation_path_obj / seq
            if seq_label_path.exists() and seq_label_path.is_dir():
                label_files = list(seq_label_path.glob('*.txt'))
                print(f"  {seq}: 标注存在，包含 {len(label_files)} 个标注文件")
            else:
                print(f"  {seq}: 标注不存在")
    else:
        print(f"错误: 标注路径不存在: {annotation_path}")
    
    # 模拟代码中的序列选择逻辑
    print(f"\n=== 模拟代码逻辑 ===")
    sequences = [d for d in data_path_obj.iterdir() if d.is_dir()]
    sequences.sort()
    print(f"所有可用序列: {[seq.name for seq in sequences]}")
    
    # 根据命令行参数选择要检测的序列
    filtered_sequences = [seq for seq in sequences if seq.name in specified_sequences]
    print(f"过滤后的序列: {[seq.name for seq in filtered_sequences]}")
    print(f"过滤后序列数量: {len(filtered_sequences)}")
    
    if len(filtered_sequences) != len(specified_sequences):
        print(f"⚠️ 警告: 指定了 {len(specified_sequences)} 个序列，但只找到了 {len(filtered_sequences)} 个")

if __name__ == "__main__":
    debug_sequences()
