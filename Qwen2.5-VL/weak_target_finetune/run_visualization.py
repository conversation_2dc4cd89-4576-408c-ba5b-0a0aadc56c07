#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速运行可视化脚本
自动使用最新的检测结果进行可视化
"""

import os
import sys
import glob
from pathlib import Path

def find_latest_detection_results():
    """查找最新的检测结果文件"""
    output_dir = Path("Output")
    if not output_dir.exists():
        return None
    
    # 查找所有检测结果文件
    result_files = list(output_dir.glob("*detection_results*.json"))
    
    if not result_files:
        return None
    
    # 返回最新修改的文件
    latest_file = max(result_files, key=lambda x: x.stat().st_mtime)
    return str(latest_file)

def main():
    """主函数"""
    print("🎨 检测结果可视化工具")
    print("="*50)
    
    # 查找最新的检测结果文件
    detection_results = find_latest_detection_results()
    
    if detection_results is None:
        print("❌ 未找到检测结果文件")
        print("请先运行 evaluate_finetuned_improved.py 生成检测结果")
        return
    
    print(f"📁 使用检测结果文件: {detection_results}")
    
    # 设置默认参数
    data_paths = [
        "/home/<USER>/Qwen/Qwen2.5-VL/new_processed_data",
        "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images"
    ]
    
    # 查找存在的数据路径
    data_path = None
    for path in data_paths:
        if os.path.exists(path):
            data_path = path
            break
    
    if data_path is None:
        print("❌ 未找到图像数据目录")
        print("请检查以下路径是否存在:")
        for path in data_paths:
            print(f"  - {path}")
        return
    
    print(f"📁 使用图像数据目录: {data_path}")
    
    # 设置输出目录
    output_dir = "visual_result"
    print(f"📁 输出目录: {output_dir}")
    
    # 构建命令
    cmd = f'python visualize_detection_results.py --detection_results "{detection_results}" --data_path "{data_path}" --output_dir "{output_dir}" --verbose'
    
    print(f"\n🚀 执行命令:")
    print(f"   {cmd}")
    print("\n" + "="*50)
    
    # 执行命令
    os.system(cmd)

if __name__ == "__main__":
    main()
