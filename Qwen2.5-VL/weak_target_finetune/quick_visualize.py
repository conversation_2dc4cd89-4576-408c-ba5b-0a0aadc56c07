#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速可视化脚本 - 修复版
自动选择正确的检测结果文件进行可视化
"""

import os
import sys
from pathlib import Path

def find_detection_results():
    """查找检测结果文件（排除评估结果）"""
    output_dir = Path("Output")
    if not output_dir.exists():
        return None
    
    # 查找所有JSON文件
    all_files = list(output_dir.glob("*.json"))
    
    # 按优先级排序检测结果文件
    detection_files = []
    evaluation_files = []
    
    for file in all_files:
        if "evaluation" in file.name:
            evaluation_files.append(file)
        elif "detection_results" in file.name:
            detection_files.append(file)
    
    print(f"📁 找到的文件:")
    for file in detection_files:
        print(f"  ✅ {file.name} (检测结果)")
    for file in evaluation_files:
        print(f"  📊 {file.name} (评估结果)")
    
    if not detection_files:
        print("❌ 未找到检测结果文件")
        return None
    
    # 按修改时间排序，返回最新的
    latest_file = max(detection_files, key=lambda x: x.stat().st_mtime)
    return str(latest_file)

def main():
    """主函数"""
    print("🎨 快速可视化工具 - 修复版")
    print("="*50)
    
    # 查找检测结果文件
    detection_results = find_detection_results()
    
    if detection_results is None:
        print("请先运行检测脚本生成检测结果")
        return
    
    print(f"📁 使用检测结果文件: {detection_results}")
    
    # 设置数据路径
    data_paths = [
        "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images",
        "/home/<USER>/Qwen/Qwen2.5-VL/new_processed_data"
    ]
    
    data_path = None
    for path in data_paths:
        if os.path.exists(path):
            data_path = path
            break
    
    if data_path is None:
        print("❌ 未找到图像数据目录")
        return
    
    print(f"📁 使用图像数据目录: {data_path}")
    
    # 设置输出目录
    output_dir = "visual_result"
    print(f"📁 输出目录: {output_dir}")
    
    # 构建并执行命令
    cmd = f'python visualize_simple.py --detection_results "{detection_results}" --data_path "{data_path}" --output_dir "{output_dir}"'
    
    print(f"\n🚀 执行命令:")
    print(f"   {cmd}")
    print("\n" + "="*50)
    
    # 执行可视化
    result = os.system(cmd)
    
    if result == 0:
        print("\n🎉 可视化完成!")
        print(f"📁 结果保存在: {output_dir}/")
        
        # 统计生成的文件
        visual_dir = Path(output_dir)
        if visual_dir.exists():
            total_frames = 0
            sequences = []
            for seq_dir in sorted(visual_dir.iterdir()):
                if seq_dir.is_dir():
                    frame_count = len(list(seq_dir.glob("*.jpg")))
                    total_frames += frame_count
                    sequences.append(f"{seq_dir.name}({frame_count}帧)")
            
            print(f"📊 生成结果: {len(sequences)} 个序列，{total_frames} 帧图像")
            print(f"📋 序列详情: {', '.join(sequences)}")
        
        print(f"\n💡 使用图像查看器打开 {output_dir}/ 目录查看结果")
        print(f"🎯 每个检测框标注了类别、置信度和时序得分")
    else:
        print("\n❌ 可视化失败")

if __name__ == "__main__":
    main()
