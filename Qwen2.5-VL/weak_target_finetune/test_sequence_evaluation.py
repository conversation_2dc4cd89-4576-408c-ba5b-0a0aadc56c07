#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试序列评估修复 - 验证所有指定序列都被包含在评估中
"""

import os
import sys
from pathlib import Path

# 添加父目录到路径
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

def test_sequence_inclusion():
    """测试序列包含逻辑"""
    
    # 模拟指定的序列
    specified_sequences = ['data_02', 'data_05', 'data_06', 'data_14', 'data_15']
    
    # 模拟检测结果（只有部分序列有结果）
    detection_results = [
        {'sequence_id': 'data_02', 'detections': 5},
        {'sequence_id': 'data_05', 'detections': 0},  # 没有检测到目标
        {'sequence_id': 'data_06', 'detections': 0},  # 没有检测到目标
        # data_14 和 data_15 完全没有检测结果
    ]
    
    # 模拟原来的逻辑（有问题的）
    print("=== 原来的逻辑（有问题）===")
    detected_sequences_old = [result['sequence_id'] for result in detection_results if result['detections'] > 0]
    print(f"指定序列: {specified_sequences}")
    print(f"有检测结果的序列: {detected_sequences_old}")
    print(f"会被评估的序列: {detected_sequences_old}")
    print(f"缺失的序列: {set(specified_sequences) - set(detected_sequences_old)}")
    
    print("\n=== 修复后的逻辑 ===")
    # 修复后的逻辑
    predictions_by_seq = {}
    for result in detection_results:
        predictions_by_seq[result['sequence_id']] = result['detections']
    
    # 确保所有指定的序列都被包含
    for seq in specified_sequences:
        if seq not in predictions_by_seq:
            predictions_by_seq[seq] = 0  # 没有检测结果
    
    print(f"指定序列: {specified_sequences}")
    print(f"会被评估的序列: {list(predictions_by_seq.keys())}")
    print(f"各序列检测数量: {predictions_by_seq}")
    print(f"是否包含所有指定序列: {set(specified_sequences) == set(predictions_by_seq.keys())}")

if __name__ == "__main__":
    test_sequence_inclusion()
