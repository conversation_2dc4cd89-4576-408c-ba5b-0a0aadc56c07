#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
红外弱小目标检测系统 - 微调版 (改进评估指标)
基于evaluate_finetuned_fixed.py，保留原有的检测格式和逻辑
使用detection_evaluator.py中的评估指标计算方法和结果显示方式
"""

import os
import re
import json
import numpy as np
from collections import defaultdict
import logging
from tqdm import tqdm
import argparse
from pathlib import Path
import sys

# 设置CUDA环境，避免设备冲突
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

import torch
from PIL import Image
import cv2
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

# 添加父目录到路径，以便导入qwen_vl_utils
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from peft import PeftModel
from qwen_vl_utils import process_vision_info

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def natural_sort_key(filename: str) -> List:
    """
    自然排序键函数，正确处理数字排序
    支持多种文件名格式：
    1. 简单数字格式：0.bmp, 1.bmp, 2.bmp, ..., 10.bmp, 11.bmp
    2. 复杂格式：fold_DJI_0043_2_00000.jpg, fold_DJI_0043_2_00001.jpg
    3. 长格式：merged_dataset_data_transform_1_wg2022_ir_053_split_06_001000.jpg
    """
    import re
    # 提取文件名中的所有数字序列
    numbers = re.findall(r'\d+', filename)
    if numbers:
        # 使用最后一个数字作为主要排序键（通常是帧序号）
        return [int(numbers[-1])]
    else:
        # 如果没有数字，按字符串排序
        return [filename.lower()]

def extract_frame_number(filename: str) -> int:
    """
    从文件名中提取帧序号
    支持多种文件名格式
    """
    import re
    # 尝试提取文件名中的所有数字
    numbers = re.findall(r'\d+', filename)
    if numbers:
        # 对于复杂格式，使用最后一个数字作为帧序号
        return int(numbers[-1])
    else:
        # 如果没有数字，返回0
        return 0

@dataclass
class DetectionResult:
    """检测结果数据类"""
    bbox: List[float]  # [x1, y1, x2, y2]
    label: str
    confidence: float
    frame_id: str
    sequence_id: str
    temporal_score: float = 0.5  # 时序一致性得分

@dataclass
class GroundTruth:
    """真实标注数据类"""
    bbox: List[float]  # [x1, y1, x2, y2]
    label: str
    frame_id: str
    sequence_id: str

class InfraredTargetDetector:
    """红外弱小目标检测器 - 支持多帧时间窗口检测，使用微调后的模型"""

    def __init__(self, base_model_path: str, lora_model_path: str = None, device: str = "auto", class_json_path: str = "class.json", frame_group_size: int = 5):
        """
        初始化检测器

        Args:
            base_model_path: 基础Qwen2.5-VL模型路径
            lora_model_path: LoRA微调模型路径（如果为None则使用原始模型）
            device: 设备类型
            class_json_path: 类别映射json路径
            frame_group_size: 时间窗口大小（帧数）
        """
        self.device = device
        self.frame_group_size = frame_group_size
        self.lora_model_path = lora_model_path

        if lora_model_path:
            logger.info(f"正在加载基础模型: {base_model_path}")
            logger.info(f"正在加载LoRA模型: {lora_model_path}")
        else:
            logger.info(f"正在加载模型: {base_model_path}")
        logger.info(f"时间窗口大小: {frame_group_size} 帧")

        # 设置多GPU策略
        import torch
        if torch.cuda.device_count() > 1:
            logger.info(f"检测到 {torch.cuda.device_count()} 个GPU，使用多GPU加速")

        # 加载基础模型
        base_model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            base_model_path,
            torch_dtype="auto",
            device_map="auto"
        )

        # 如果有LoRA模型，则加载微调后的模型
        if lora_model_path:
            self.model = PeftModel.from_pretrained(base_model, lora_model_path)
            self.model.eval()
            logger.info("LoRA微调模型加载完成")
        else:
            self.model = base_model

        self.processor = AutoProcessor.from_pretrained(base_model_path)

        # 使用针对弱小目标优化的检测提示词
        self.detection_prompt = (
            "请识别图像中所有(数目为1个)弱小目标，包括无人机等红外目标（这里都是无人机）；同时请重点检测每帧中的微小目标，特别是移动的小白点或小亮点（此类目标也属于需识别的弱小目标范畴）。\n"
            "请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一："
            "drone, car, ship, bus, pedestrian, cyclist。\n"
            "格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]"
        )

        # 加载类别映射
        self.class_map, self.class_map_rev = load_class_map(class_json_path)

        logger.info("模型加载完成")
    
    def get_sorted_image_files(self, sequence_path: str) -> List[Path]:
        """
        获取正确排序的图像文件列表

        Args:
            sequence_path: 序列路径

        Returns:
            按数字顺序排序的图像文件列表
        """
        image_files = []
        for ext in ['*.bmp', '*.jpg', '*.jpeg', '*.png']:
            image_files.extend(Path(sequence_path).glob(ext))

        # 使用自然排序，确保正确的数字顺序
        image_files.sort(key=lambda x: natural_sort_key(x.name))

        return image_files

    def detect_frame_group(self, image_paths: List[str], frame_indices: List[int]) -> List[DetectionResult]:
        """
        检测帧组中的目标 - 简化版本，直接使用单帧检测但保留时序信息
        """
        results = []
        for i, (img_path, frame_idx) in enumerate(zip(image_paths, frame_indices)):
            frame_results = self.detect_single_frame(img_path)
            for result in frame_results:
                result.frame_id = str(frame_idx)
                result.temporal_score = 0.8  # 时间窗口内的检测给予更高时序得分
            results.extend(frame_results)
        return results

    def detect_single_frame(self, image_path: str) -> List[DetectionResult]:
        """
        检测单帧图像中的目标
        """
        try:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image",
                            "image": image_path,
                        },
                        {"type": "text", "text": self.detection_prompt},
                    ],
                }
            ]
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            image_inputs, video_inputs = process_vision_info(messages)
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
            )
            inputs = inputs.to(self.model.device)
            with torch.no_grad():
                generated_ids = self.model.generate(**inputs, max_new_tokens=512)
                generated_ids_trimmed = [
                    out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
                ]
                output_text = self.processor.batch_decode(
                    generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
                )
            # === 打印模型原始输出 ===
            logger.info(f"模型原始输出（{os.path.basename(image_path)}）：{output_text[0]}")
            # ======================
            results = self._parse_detection_output(output_text[0])
            return results
        except Exception as e:
            logger.error(f"检测单帧图像失败: {e}")
            return []

    def _parse_detection_output(self, output_text: str) -> List[DetectionResult]:
        results = []
        try:
            output_text = output_text.strip()
            # 尝试提取所有JSON对象
            json_objs = re.findall(r'\{[^\{\}]*\}', output_text)
            for obj_str in json_objs:
                try:
                    det = json.loads(obj_str)
                    bbox = det.get('bbox') or det.get('bbox_2d')
                    if bbox is not None:
                        label = det.get('label', 'unknown')
                        label = self.class_map_rev.get(label, label)
                        result = DetectionResult(
                            bbox=bbox,
                            label=label,
                            confidence=det.get('confidence', 0.5),
                            frame_id='0',
                            sequence_id='',
                            temporal_score=0.5
                        )
                        results.append(result)
                except Exception:
                    continue
            # 如果正则没找到，尝试原有方式
            if not results:
                json_start = output_text.find('[')
                json_end = output_text.rfind(']')
                if json_start != -1 and json_end != -1:
                    json_str = output_text[json_start:json_end+1]
                    detections = json.loads(json_str)
                    for det in detections:
                        if isinstance(det, dict):
                            bbox = det.get('bbox') or det.get('bbox_2d')
                            if bbox is not None:
                                label = det.get('label', 'unknown')
                                label = self.class_map_rev.get(label, label)
                                result = DetectionResult(
                                    bbox=bbox,
                                    label=label,
                                    confidence=det.get('confidence', 0.5),
                                    frame_id='0',
                                    sequence_id='',
                                    temporal_score=0.5
                                )
                                results.append(result)
        except Exception as e:
            logger.warning(f"解析检测输出失败: {e}")
        return results

    def detect_sequence_with_temporal_window(self, sequence_path: str, sequence_id: str, sample_ratio: float = 0.01) -> List[DetectionResult]:
        """
        使用时间窗口检测序列中的目标
        """
        results = []

        # 获取正确排序的图像文件
        image_files = self.get_sorted_image_files(sequence_path)

        # 计算需要处理的帧数
        total_frames = len(image_files)
        frames_to_process = max(self.frame_group_size, int(total_frames * sample_ratio))
        selected_frames = image_files[:frames_to_process]

        logger.info(f"开始检测序列 {sequence_id}，共 {total_frames} 帧，处理前 {frames_to_process} 帧 ({sample_ratio*100:.2f}%)")

        # 按时间窗口分组处理
        for start_idx in range(0, len(selected_frames), self.frame_group_size):
            end_idx = min(start_idx + self.frame_group_size, len(selected_frames))
            frame_group = selected_frames[start_idx:end_idx]

            # 获取图像路径和帧索引 - 修复：使用连续的索引而不是文件名中的数字
            image_paths = [str(img_file) for img_file in frame_group]
            # 使用在selected_frames中的相对索引，确保从0开始连续
            frame_indices = [start_idx + i for i in range(len(frame_group))]

            # 使用简化的帧组检测
            group_results = self.detect_frame_group(image_paths, frame_indices)

            # 设置序列ID
            for result in group_results:
                result.sequence_id = sequence_id

            results.extend(group_results)

            logger.info(f"序列 {sequence_id}: 处理窗口 {start_idx//self.frame_group_size + 1}，"
                       f"帧范围 {start_idx}-{end_idx-1}，检测到 {len(group_results)} 个目标")

            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

        logger.info(f"序列 {sequence_id} 检测完成，检测到 {len(results)} 个目标")
        return results

    def detect_sequence(self, sequence_path: str, sequence_id: str, sample_ratio: float = 0.01) -> List[DetectionResult]:
        """
        检测序列中的目标，只处理前sample_ratio比例的数据
        """
        results = []
        image_files = self.get_sorted_image_files(sequence_path)

        # 计算需要处理的帧数
        total_frames = len(image_files)
        frames_to_process = max(1, int(total_frames * sample_ratio))
        selected_frames = image_files[:frames_to_process]

        logger.info(f"开始检测序列 {sequence_id}，共 {total_frames} 帧，处理前 {frames_to_process} 帧 ({sample_ratio*100:.2f}%)")

        for frame_idx, image_file in enumerate(tqdm(selected_frames, desc=f"检测序列 {sequence_id}")):
            frame_results = self.detect_single_frame(str(image_file))
            frame_id = str(extract_frame_number(image_file.name))
            for result in frame_results:
                result.frame_id = frame_id
                result.sequence_id = sequence_id
            results.extend(frame_results)
            if (frame_idx + 1) % 10 == 0:
                logger.info(f"序列 {sequence_id}: 已处理 {frame_idx + 1}/{frames_to_process} 帧，当前帧检测到 {len(frame_results)} 个目标")
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
        return results

class ImprovedDetectionEvaluator:
    """改进的检测结果评估器 - 基于detection_evaluator.py的评估方法"""

    def __init__(self, iou_threshold: float = 0.3, consistency_iou_threshold: float = 0.3, stability_threshold: float = 0.8, class_names: List[str] = None):
        """
        初始化评估器

        Args:
            iou_threshold: IoU阈值
            consistency_iou_threshold: 时序一致性IoU阈值
            stability_threshold: 时空稳定性阈值
            class_names: 类别名称列表
        """
        self.iou_threshold = iou_threshold
        self.consistency_iou_threshold = consistency_iou_threshold
        self.stability_threshold = stability_threshold
        self.class_names = class_names or ['drone', 'car', 'ship', 'bus', 'pedestrian', 'cyclist']

        # 每个视频的结果
        self.video_results = {}

    def convert_detection_to_yolo_format(self, detection: DetectionResult, img_width: int = 640, img_height: int = 640) -> Dict:
        """
        将DetectionResult转换为YOLO格式的字典
        """
        # 获取类别ID
        class_id = self.class_names.index(detection.label) if detection.label in self.class_names else 0

        # 转换bbox格式：从[x1, y1, x2, y2]到[x_center, y_center, width, height]（归一化）
        x1, y1, x2, y2 = detection.bbox
        x_center = (x1 + x2) / 2 / img_width
        y_center = (y1 + y2) / 2 / img_height
        width = (x2 - x1) / img_width
        height = (y2 - y1) / img_height

        return {
            'class_id': class_id,
            'x_center': x_center,
            'y_center': y_center,
            'width': width,
            'height': height,
            'confidence': detection.confidence
        }

    def convert_groundtruth_to_yolo_format(self, gt: GroundTruth, img_width: int = 640, img_height: int = 640) -> Dict:
        """
        将GroundTruth转换为YOLO格式的字典
        """
        # 获取类别ID
        class_id = self.class_names.index(gt.label) if gt.label in self.class_names else 0

        # 转换bbox格式：从[x1, y1, x2, y2]到[x_center, y_center, width, height]（归一化）
        x1, y1, x2, y2 = gt.bbox
        x_center = (x1 + x2) / 2 / img_width
        y_center = (y1 + y2) / 2 / img_height
        width = (x2 - x1) / img_width
        height = (y2 - y1) / img_height

        return {
            'class_id': class_id,
            'x_center': x_center,
            'y_center': y_center,
            'width': width,
            'height': height,
            'confidence': 1.0
        }

    def calculate_iou(self, box1: Dict, box2: Dict) -> float:
        """
        计算两个边界框的IoU（YOLO格式）
        """
        # 转换为左上角和右下角坐标
        x1_min = box1['x_center'] - box1['width'] / 2
        y1_min = box1['y_center'] - box1['height'] / 2
        x1_max = box1['x_center'] + box1['width'] / 2
        y1_max = box1['y_center'] + box1['height'] / 2

        x2_min = box2['x_center'] - box2['width'] / 2
        y2_min = box2['y_center'] - box2['height'] / 2
        x2_max = box2['x_center'] + box2['width'] / 2
        y2_max = box2['y_center'] + box2['height'] / 2

        # 计算交集
        inter_x_min = max(x1_min, x2_min)
        inter_y_min = max(y1_min, y2_min)
        inter_x_max = min(x1_max, x2_max)
        inter_y_max = min(y1_max, y2_max)

        if inter_x_max <= inter_x_min or inter_y_max <= inter_y_min:
            return 0.0

        inter_area = (inter_x_max - inter_x_min) * (inter_y_max - inter_y_min)

        # 计算并集
        area1 = box1['width'] * box1['height']
        area2 = box2['width'] * box2['height']
        union_area = area1 + area2 - inter_area

        return inter_area / union_area if union_area > 0 else 0.0

    def match_boxes(self, gt_boxes: List[Dict], pred_boxes: List[Dict], iou_threshold: float = None) -> Tuple:
        """
        匹配真实框和预测框
        """
        if iou_threshold is None:
            iou_threshold = self.iou_threshold

        matched_gt = set()
        matched_pred = set()
        matches = []

        # 按置信度排序预测框
        pred_boxes_sorted = sorted(enumerate(pred_boxes),
                                 key=lambda x: x[1]['confidence'], reverse=True)

        for pred_idx, pred_box in pred_boxes_sorted:
            best_iou = 0
            best_gt_idx = -1

            for gt_idx, gt_box in enumerate(gt_boxes):
                if gt_idx in matched_gt:
                    continue

                # 只匹配相同类别的框
                if gt_box['class_id'] != pred_box['class_id']:
                    continue

                iou = self.calculate_iou(gt_box, pred_box)
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = gt_idx

            if best_iou >= iou_threshold and best_gt_idx not in matched_gt:
                matches.append((best_gt_idx, pred_idx, best_iou))
                matched_gt.add(best_gt_idx)
                matched_pred.add(pred_idx)

        return matches, matched_gt, matched_pred

    def check_frame_consistency(self, gt_boxes: List[Dict], pred_boxes: List[Dict]) -> bool:
        """
        检查单帧的时序一致性
        规则：
        - 当真值目标 <= 5个时，要求所有目标检出，且类别正确，且IoU > consistency_iou_threshold
        - 当真值目标 > 5个时，要求80%的目标检出，且类别正确，且IoU > consistency_iou_threshold
        """
        if len(gt_boxes) == 0:
            # 如果该帧没有真实目标，则认为是一致的（空帧）
            return True

        if len(pred_boxes) == 0:
            # 如果有真实目标但没有预测，则不一致
            return False

        # 使用时序一致性专用的匹配函数
        matches, matched_gt, matched_pred = self.match_boxes(gt_boxes, pred_boxes, self.consistency_iou_threshold)

        # 计算检出的目标数量
        detected_targets = len(matched_gt)
        total_targets = len(gt_boxes)

        # 根据目标数量应用不同的一致性标准
        if total_targets <= 5:
            # 目标数量 <= 5：要求所有目标都被检出
            required_detections = total_targets
            is_consistent = detected_targets >= required_detections
        else:
            # 目标数量 > 5：要求80%的目标被检出
            required_detections = int(total_targets * 0.8)
            is_consistent = detected_targets >= required_detections

        return is_consistent

    def evaluate_frame_pair(self, gt_detections: List[GroundTruth], pred_detections: List[DetectionResult], video_stats: Dict):
        """评估一对对应的真实标注和预测结果"""
        # 转换为YOLO格式
        gt_boxes = [self.convert_groundtruth_to_yolo_format(gt) for gt in gt_detections]
        pred_boxes = [self.convert_detection_to_yolo_format(pred) for pred in pred_detections]

        # 检查时序一致性
        is_consistent = self.check_frame_consistency(gt_boxes, pred_boxes)
        video_stats['consistency_frames'] += 1 if is_consistent else 0
        video_stats['total_frames'] += 1

        # 记录详细的一致性统计信息（用于调试和分析）
        gt_count = len(gt_boxes)
        if gt_count > 0:
            if gt_count <= 5:
                video_stats['frames_with_targets_le5'] += 1
                if is_consistent:
                    video_stats['consistent_frames_le5'] += 1
            else:
                video_stats['frames_with_targets_gt5'] += 1
                if is_consistent:
                    video_stats['consistent_frames_gt5'] += 1

        # 按类别分组
        gt_by_class = defaultdict(list)
        pred_by_class = defaultdict(list)

        for box in gt_boxes:
            gt_by_class[box['class_id']].append(box)
            video_stats['total_gt'][box['class_id']] += 1

        for box in pred_boxes:
            pred_by_class[box['class_id']].append(box)
            video_stats['total_pred'][box['class_id']] += 1

        # 获取所有出现的类别
        all_classes = set(gt_by_class.keys()) | set(pred_by_class.keys())

        for class_id in all_classes:
            gt_class_boxes = gt_by_class[class_id]
            pred_class_boxes = pred_by_class[class_id]

            if len(gt_class_boxes) == 0 and len(pred_class_boxes) == 0:
                continue

            # 匹配该类别的框（使用标准IoU阈值）
            matches, matched_gt, matched_pred = self.match_boxes(gt_class_boxes, pred_class_boxes)

            # 统计TP, FP, FN
            tp_count = len(matches)
            fp_count = len(pred_class_boxes) - len(matched_pred)
            fn_count = len(gt_class_boxes) - len(matched_gt)

            video_stats['tp'][class_id] += tp_count
            video_stats['fp'][class_id] += fp_count
            video_stats['fn'][class_id] += fn_count

    def calculate_metrics_for_stats(self, stats: Dict, use_macro_average: bool = True) -> Dict:
        """
        根据统计数据计算指标
        use_macro_average: 是否使用宏平均计算总体指标
        """
        results = {}
        all_classes = set(stats['tp'].keys()) | set(stats['fp'].keys()) | set(stats['fn'].keys())

        # 用于宏平均的指标列表
        class_precisions = []
        class_recalls = []
        class_f1s = []
        class_false_alarm_rates = []

        # 用于微平均的总计数
        total_tp = 0
        total_fp = 0
        total_fn = 0

        # 记录参与宏平均计算的类别（数据集中存在的类别）
        valid_classes = []

        for class_id in sorted(all_classes):
            tp = stats['tp'][class_id]
            fp = stats['fp'][class_id]
            fn = stats['fn'][class_id]

            # 计算召回率 (Recall)
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0

            # 计算精确率 (Precision)
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0

            # 计算虚警率 (False Alarm Rate = 1 - Precision)
            false_alarm_rate = 1.0 - precision

            # 计算F1分数
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

            class_name = self.class_names[class_id] if class_id < len(self.class_names) else f"Class_{class_id}"

            results[class_id] = {
                'class_name': class_name,
                'tp': tp,
                'fp': fp,
                'fn': fn,
                'recall': recall,
                'precision': precision,
                'false_alarm_rate': false_alarm_rate,
                'f1': f1,
                'total_gt': stats['total_gt'][class_id],
                'total_pred': stats['total_pred'][class_id]
            }

            # 判断是否为数据集中存在的类别：TP + FN > 0（有真实目标的类别）
            if tp + fn > 0:  # 数据集中存在的类别
                class_precisions.append(precision)
                class_recalls.append(recall)
                class_f1s.append(f1)
                class_false_alarm_rates.append(false_alarm_rate)
                valid_classes.append(class_id)

            # 累计用于微平均
            total_tp += tp
            total_fp += fp
            total_fn += fn

        # 计算总体指标
        if use_macro_average and len(class_precisions) > 0:
            # 宏平均：先计算各有效类别指标，再取平均
            overall_recall = sum(class_recalls) / len(class_recalls)
            overall_precision = sum(class_precisions) / len(class_precisions)
            overall_false_alarm_rate = sum(class_false_alarm_rates) / len(class_false_alarm_rates)
            overall_f1 = sum(class_f1s) / len(class_f1s)
            averaging_method = "macro"
            valid_classes_count = len(valid_classes)
        else:
            # 微平均：先累加所有TP/FP/FN，再计算指标
            overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0
            overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0.0
            overall_false_alarm_rate = 1.0 - overall_precision
            overall_f1 = 2 * (overall_precision * overall_recall) / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0.0
            averaging_method = "micro"
            valid_classes_count = len(all_classes)

        # 计算时序一致性
        temporal_consistency = stats['consistency_frames'] / stats['total_frames'] if stats['total_frames'] > 0 else 0.0

        results['overall'] = {
            'tp': total_tp,
            'fp': total_fp,
            'fn': total_fn,
            'recall': overall_recall,
            'precision': overall_precision,
            'false_alarm_rate': overall_false_alarm_rate,
            'f1': overall_f1,
            'total_gt': sum(stats['total_gt'].values()),
            'total_pred': sum(stats['total_pred'].values()),
            'averaging_method': averaging_method,
            'valid_classes_count': valid_classes_count,
            'valid_classes': valid_classes if use_macro_average else list(all_classes),
            'temporal_consistency': temporal_consistency,
            'consistent_frames': stats['consistency_frames'],
            'total_frames': stats['total_frames'],
            # 添加详细的一致性统计
            'frames_with_targets_le5': stats.get('frames_with_targets_le5', 0),
            'consistent_frames_le5': stats.get('consistent_frames_le5', 0),
            'frames_with_targets_gt5': stats.get('frames_with_targets_gt5', 0),
            'consistent_frames_gt5': stats.get('consistent_frames_gt5', 0)
        }

        return results

    def evaluate_sequence(self, predictions: List[DetectionResult], ground_truth: List[GroundTruth]) -> Dict:
        """
        评估单个序列的检测结果
        """
        # 按帧分组
        pred_by_frame = defaultdict(list)
        gt_by_frame = defaultdict(list)

        for pred in predictions:
            pred_by_frame[pred.frame_id].append(pred)

        for gt in ground_truth:
            gt_by_frame[gt.frame_id].append(gt)

        # 初始化该序列的统计数据
        video_stats = {
            'tp': defaultdict(int),
            'fp': defaultdict(int),
            'fn': defaultdict(int),
            'total_gt': defaultdict(int),
            'total_pred': defaultdict(int),
            'consistency_frames': 0,  # 一致的帧数
            'total_frames': 0,        # 总帧数
            # 详细的一致性统计
            'frames_with_targets_le5': 0,  # 目标数<=5的帧数
            'consistent_frames_le5': 0,    # 目标数<=5且一致的帧数
            'frames_with_targets_gt5': 0,  # 目标数>5的帧数
            'consistent_frames_gt5': 0     # 目标数>5且一致的帧数
        }

        all_frames = set(pred_by_frame.keys()) | set(gt_by_frame.keys())

        for frame_id in all_frames:
            frame_preds = pred_by_frame[frame_id]
            frame_gts = gt_by_frame[frame_id]

            # 评估当前帧
            self.evaluate_frame_pair(frame_gts, frame_preds, video_stats)

        # 计算该序列的指标（使用宏平均）
        sequence_metrics = self.calculate_metrics_for_stats(video_stats, use_macro_average=True)

        return sequence_metrics

    def evaluate_all_sequences(self, all_predictions: Dict[str, List[DetectionResult]],
                              all_ground_truth: Dict[str, List[GroundTruth]]) -> Dict:
        """
        评估所有序列的检测结果
        """
        sequence_results = {}

        for seq_id in tqdm(all_predictions.keys(), desc="评估序列", unit="seq"):
            if seq_id in all_ground_truth:
                seq_result = self.evaluate_sequence(
                    all_predictions[seq_id],
                    all_ground_truth[seq_id]
                )
                sequence_results[seq_id] = seq_result

                # 保存该序列的结果
                self.video_results[seq_id] = {
                    'frames_processed': seq_result['overall']['total_frames'],
                    'frames_gt': seq_result['overall']['total_frames'],
                    'frames_pred': seq_result['overall']['total_frames'],
                    'metrics': seq_result
                }

                # 输出当前序列的评估结果
                logger.info(f"序列 {seq_id} 评估完成: 精确率={seq_result['overall']['precision']:.4f}, "
                           f"召回率={seq_result['overall']['recall']:.4f}, "
                           f"时序一致性={seq_result['overall']['temporal_consistency']:.4f}")

        # 计算总体指标（直接基于视频总体指标进行平均）
        overall_results = self.calculate_overall_metrics_direct_video_average()

        return {
            'sequence_results': sequence_results,
            'overall_results': overall_results
        }

    def calculate_overall_metrics_direct_video_average(self) -> Dict:
        """直接基于视频总体指标进行平均"""
        if not self.video_results:
            return {}

        # 直接收集各视频的总体指标（不分类别）
        video_overall_precisions = []
        video_overall_recalls = []
        video_overall_f1s = []
        video_overall_false_alarm_rates = []
        video_temporal_consistencies = []

        # 累计统计用于显示
        total_stats = {
            'tp': 0,
            'fp': 0,
            'fn': 0,
            'total_gt': 0,
            'total_pred': 0,
            'total_frames': 0,
            'total_consistent_frames': 0,
            'total_frames_with_targets_le5': 0,
            'total_consistent_frames_le5': 0,
            'total_frames_with_targets_gt5': 0,
            'total_consistent_frames_gt5': 0
        }

        # 统计时空稳定性
        stable_videos = 0  # 时序一致性超过阈值的视频数

        for video_name, video_result in self.video_results.items():
            metrics = video_result['metrics']

            # 直接收集视频总体指标
            if 'overall' in metrics:
                overall_metric = metrics['overall']
                video_overall_precisions.append(overall_metric['precision'])
                video_overall_recalls.append(overall_metric['recall'])
                video_overall_f1s.append(overall_metric['f1'])
                video_overall_false_alarm_rates.append(overall_metric['false_alarm_rate'])
                video_temporal_consistencies.append(overall_metric['temporal_consistency'])

                # 累计统计
                total_stats['tp'] += overall_metric['tp']
                total_stats['fp'] += overall_metric['fp']
                total_stats['fn'] += overall_metric['fn']
                total_stats['total_gt'] += overall_metric['total_gt']
                total_stats['total_pred'] += overall_metric['total_pred']
                total_stats['total_frames'] += overall_metric['total_frames']
                total_stats['total_consistent_frames'] += overall_metric['consistent_frames']
                total_stats['total_frames_with_targets_le5'] += overall_metric.get('frames_with_targets_le5', 0)
                total_stats['total_consistent_frames_le5'] += overall_metric.get('consistent_frames_le5', 0)
                total_stats['total_frames_with_targets_gt5'] += overall_metric.get('frames_with_targets_gt5', 0)
                total_stats['total_consistent_frames_gt5'] += overall_metric.get('consistent_frames_gt5', 0)

                # 检查是否为稳定视频
                if overall_metric['temporal_consistency'] >= self.stability_threshold:
                    stable_videos += 1

        # 直接计算总体的视频级平均指标
        overall_precision = sum(video_overall_precisions) / len(video_overall_precisions) if video_overall_precisions else 0.0
        overall_recall = sum(video_overall_recalls) / len(video_overall_recalls) if video_overall_recalls else 0.0
        overall_f1 = sum(video_overall_f1s) / len(video_overall_f1s) if video_overall_f1s else 0.0
        overall_false_alarm_rate = sum(video_overall_false_alarm_rates) / len(video_overall_false_alarm_rates) if video_overall_false_alarm_rates else 0.0
        overall_temporal_consistency = sum(video_temporal_consistencies) / len(video_temporal_consistencies) if video_temporal_consistencies else 0.0

        # 计算时空序列稳定性
        spatiotemporal_stability = stable_videos / len(self.video_results) if len(self.video_results) > 0 else 0.0

        results = {
            'overall': {
                'tp': total_stats['tp'],
                'fp': total_stats['fp'],
                'fn': total_stats['fn'],
                'recall': overall_recall,
                'precision': overall_precision,
                'false_alarm_rate': overall_false_alarm_rate,
                'f1': overall_f1,
                'total_gt': total_stats['total_gt'],
                'total_pred': total_stats['total_pred'],
                'averaging_method': 'direct_video_level',
                'video_count': len(self.video_results),
                'temporal_consistency': overall_temporal_consistency,
                'total_frames': total_stats['total_frames'],
                'total_consistent_frames': total_stats['total_consistent_frames'],
                'spatiotemporal_stability': spatiotemporal_stability,
                'stable_videos': stable_videos,
                'stability_threshold': self.stability_threshold,
                'consistency_iou_threshold': self.consistency_iou_threshold,
                # 添加详细的一致性统计
                'total_frames_with_targets_le5': total_stats['total_frames_with_targets_le5'],
                'total_consistent_frames_le5': total_stats['total_consistent_frames_le5'],
                'total_frames_with_targets_gt5': total_stats['total_frames_with_targets_gt5'],
                'total_consistent_frames_gt5': total_stats['total_consistent_frames_gt5']
            }
        }

        return results

    def print_overall_results(self, overall_metrics: Dict):
        """打印总体结果 - 显示每个视频的性能指标"""
        print("\n" + "="*88)
        print("总体评估结果 (各视频性能指标)")
        print("="*88)

        if not overall_metrics or not self.video_results:
            print("没有结果可显示")
            return

        # 显示各视频的性能指标
        print(f"{'视频名称':<20} {'召回率':<8} {'精确率':<8} {'虚警率':<8} {'时序一致性':<10} {'TP':<6} {'FP':<6} {'FN':<6} {'帧数':<6}")
        print("-" * 88)

        # 按视频名称排序显示各视频结果
        for video_name in sorted(self.video_results.keys()):
            video_result = self.video_results[video_name]
            metrics = video_result['metrics']

            if 'overall' in metrics:
                r = metrics['overall']
                frames = video_result['frames_processed']
                temporal_consistency = r['temporal_consistency']
                stability_mark = "✓" if temporal_consistency >= self.stability_threshold else " "

                print(f"{video_name:<20} {r['recall']:<8.3f} {r['precision']:<8.3f} {r['false_alarm_rate']:<8.3f} "
                      f"{temporal_consistency:<9.3f}{stability_mark} {r['tp']:<6} {r['fp']:<6} {r['fn']:<6} {frames:<6}")

        # 显示总体平均结果
        if 'overall' in overall_metrics:
            print("-" * 88)
            r = overall_metrics['overall']
            video_count = r.get('video_count', 0)
            total_frames = sum(video_result['frames_processed'] for video_result in self.video_results.values())

            print(f"{'总体平均':<20} {r['recall']:<8.3f} {r['precision']:<8.3f} {r['false_alarm_rate']:<8.3f} "
                  f"{r['temporal_consistency']:<10.3f} {r['tp']:<6} {r['fp']:<6} {r['fn']:<6} {total_frames:<6}")

            print(f"\n详细统计:")
            print(f"目标识别精度 - 平均召回率: {r['recall']:.4f}")
            print(f"目标识别精度 - 平均精确率: {r['precision']:.4f}")
            print(f"目标识别精度 - F1分数: {r['f1']:.4f}")
            print(f"时空序列稳定性: {r['spatiotemporal_stability']:.4f} ({r['stable_videos']}/{video_count} 个视频超过{r['stability_threshold']:.0%}阈值)")
            print(f"总真实目标数: {r['total_gt']}")
            print(f"总预测目标数: {r['total_pred']}")
            print(f"总处理帧数: {total_frames}")
            print(f"IoU阈值: {self.iou_threshold}")
            print(f"评估视频数: {video_count}")
            print(f"时序一致性IoU阈值: {r['consistency_iou_threshold']}")

            # 计算总体的≤5目标和>5目标一致性率
            total_frames_le5 = r.get('total_frames_with_targets_le5', 0)
            total_consistent_le5 = r.get('total_consistent_frames_le5', 0)
            total_frames_gt5 = r.get('total_frames_with_targets_gt5', 0)
            total_consistent_gt5 = r.get('total_consistent_frames_gt5', 0)

            overall_le5_rate = total_consistent_le5 / total_frames_le5 if total_frames_le5 > 0 else 0.0
            overall_gt5_rate = total_consistent_gt5 / total_frames_gt5 if total_frames_gt5 > 0 else 0.0

            print(f"")
            print(f"时序一致性统计:")
            print(f"  目标数≤5帧: {total_consistent_le5}/{total_frames_le5} ({overall_le5_rate:.3f}) - 要求100%检出")
            print(f"  目标数>5帧: {total_consistent_gt5}/{total_frames_gt5} ({overall_gt5_rate:.3f}) - 要求80%检出")
            print(f"")
            print(f"时序一致性规则:")
            print(f"  - 目标数 ≤ 5: 要求所有目标检出，且类别正确，且IoU > {r['consistency_iou_threshold']}")
            print(f"  - 目标数 > 5: 要求80%目标检出，且类别正确，且IoU > {r['consistency_iou_threshold']}")
            print(f"总体指标计算方式: 直接视频级平均")

def load_class_map(class_json_path: str):
    with open(class_json_path, 'r', encoding='utf-8') as f:
        class_map = json.load(f)
    # 反向映射：所有英文名映射成自己
    class_map_rev = {v: v for v in class_map.values()}
    # 可扩展：如有中英文对照，可在此补充
    zh2en = {"无人机": "drone", "汽车": "car", "轮船": "ship", "公交车": "bus", "行人": "pedestrian", "骑行者": "cyclist", "弱小目标": "unknown"}
    class_map_rev.update(zh2en)
    return class_map, class_map_rev # class_map是原始类别映射，class_map_rev是中英文名到英文名的映射

def load_yolo_ground_truth_with_natural_sort(labels_root: str, class_json_path: str, img_root: str, sample_ratio: float = 0.01, target_sequences: list = None):
    """
    加载YOLO格式的真实标注数据，使用自然排序，只加载前sample_ratio比例的标注

    Args:
        labels_root: 标注文件根目录
        class_json_path: 类别映射文件路径
        img_root: 图像文件根目录
        sample_ratio: 采样比例，默认0.01表示前1%
        target_sequences: 目标序列列表，如果指定则只加载这些序列
    """
    class_map, _ = load_class_map(class_json_path)
    ground_truth = defaultdict(list)

    for seq in sorted(os.listdir(labels_root)):
        seq_path = os.path.join(labels_root, seq)
        if not os.path.isdir(seq_path):
            continue

        # 如果指定了目标序列，只处理这些序列
        if target_sequences and seq not in target_sequences:
            continue

        # 获取所有标注文件并使用自然排序
        label_files = []
        for label_file in os.listdir(seq_path):
            if label_file.endswith('.txt'):
                label_files.append(label_file)

        # 使用自然排序确保正确的数字顺序
        label_files.sort(key=lambda x: natural_sort_key(x))

        # 只处理前sample_ratio比例的标注文件
        total_labels = len(label_files)
        labels_to_process = max(1, int(total_labels * sample_ratio))
        selected_labels = label_files[:labels_to_process]

        logger.info(f"序列 {seq} 标注: 共 {total_labels} 个文件，处理前 {labels_to_process} 个 ({sample_ratio*100:.2f}%)")

        for idx, label_file in enumerate(selected_labels):
            label_path = os.path.join(seq_path, label_file)
            stem = os.path.splitext(label_file)[0]
            # 修复：使用连续的索引作为frame_id，确保与检测结果匹配
            frame_id = str(idx)

            # 查找对应的图像文件
            img_file = None
            for ext in ['.bmp', '.jpg', '.jpeg', '.png']:
                candidate = os.path.join(img_root, seq, stem + ext)
                if os.path.exists(candidate):
                    img_file = candidate
                    break

            if img_file is None:
                continue

            # 获取图像尺寸
            with Image.open(img_file) as img:
                w, h = img.size

            # 解析标注文件
            has_annotations = False
            with open(label_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    parts = line.split()
                    if len(parts) < 5:
                        continue
                    cls_id, cx, cy, bw, bh = parts[:5]
                    label = class_map.get(str(cls_id), 'unknown')
                    cx, cy, bw, bh = map(float, [cx, cy, bw, bh])

                    # 转换为绝对坐标
                    x1 = (cx - bw/2) * w
                    y1 = (cy - bh/2) * h
                    x2 = (cx + bw/2) * w
                    y2 = (cy + bh/2) * h

                    gt = GroundTruth(
                        bbox=[x1, y1, x2, y2],
                        label=label,
                        frame_id=frame_id,
                        sequence_id=seq
                    )
                    ground_truth[seq].append(gt)
                    has_annotations = True

            # 确保即使是空标注文件也在ground_truth中有对应的序列条目
            # 这样空标注帧可以被正确评估为真负样本
            if not has_annotations:
                # 为空标注文件创建序列条目（如果还不存在）
                if seq not in ground_truth:
                    ground_truth[seq] = []

    return ground_truth

def save_results(results: List[DetectionResult], output_path: str):
    """
    保存检测结果

    Args:
        results: 检测结果
        output_path: 输出文件路径
    """
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    output_data = []
    for result in results:
        output_data.append({
            'sequence_id': result.sequence_id,
            'frame_id': result.frame_id,
            'bbox': result.bbox,
            'label': result.label,
            'confidence': result.confidence,
            'temporal_score': result.temporal_score
        })

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)

    logger.info(f"检测结果已保存到: {output_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='红外弱小目标检测系统 - 改进评估指标版')
    parser.add_argument('--base_model_path', type=str, required=True, help='基础Qwen2.5-VL模型路径')
    parser.add_argument('--lora_model_path', type=str, help='LoRA微调模型路径（可选）')
    parser.add_argument('--data_path', type=str, required=True, help='数据集路径')
    parser.add_argument('--output_path', type=str, default='Output/finetuned_detection_results_improved.json', help='输出结果路径')
    parser.add_argument('--annotation_path', type=str, help='真实标注文件路径（用于评估，可为labels目录）')
    parser.add_argument('--class_json', type=str, default='class.json', help='类别映射json路径')
    parser.add_argument('--device', type=str, default='auto', help='设备类型')
    parser.add_argument('--iou_threshold', type=float, default=0.3, help='IoU阈值')
    parser.add_argument('--consistency_iou_threshold', type=float, default=0.3, help='时序一致性IoU阈值')
    parser.add_argument('--stability_threshold', type=float, default=0.8, help='时空稳定性阈值')
    parser.add_argument('--sample_ratio', type=float, default=0.01, help='每个序列的采样比例，默认0.01表示前1%')
    parser.add_argument('--frame_group_size', type=int, default=5, help='时间窗口大小（帧数），建议5-8帧')
    parser.add_argument('--sequences', type=str, nargs='*', help='指定要检测的序列名称，不指定则检测所有序列')

    args = parser.parse_args()

    # 初始化时序检测器
    detector = InfraredTargetDetector(
        args.base_model_path,
        args.lora_model_path,
        args.device,
        args.class_json,
        args.frame_group_size
    )

    # 获取所有序列
    data_path = Path(args.data_path)
    sequences = [d for d in data_path.iterdir() if d.is_dir()]
    sequences.sort()

    # 根据命令行参数选择要检测的序列
    if args.sequences:
        sequences = [seq for seq in sequences if seq.name in args.sequences]
        logger.info(f"指定检测 {len(sequences)} 个序列: {[seq.name for seq in sequences]}")
    else:
        logger.info(f"检测所有 {len(sequences)} 个序列: {[seq.name for seq in sequences]}")

    # 检测序列
    all_results = []
    print(f"\n开始检测 {len(sequences)} 个序列，每个序列处理前 {args.sample_ratio*100:.2f}% 的数据...")
    print(f"使用 {args.frame_group_size} 帧时间窗口进行多帧检测")
    total_detections = 0

    for seq_idx, seq_dir in enumerate(tqdm(sequences, desc="检测序列", unit="seq")):
        seq_id = seq_dir.name
        logger.info(f"开始检测序列: {seq_id} ({seq_idx+1}/{len(sequences)})")

        # 使用时间窗口检测
        seq_results = detector.detect_sequence_with_temporal_window(str(seq_dir), seq_id, args.sample_ratio)

        if len(seq_results) > 0:
            logger.info(f"序列 {seq_id} 检测结果前2个: {[(r.frame_id, r.bbox, r.temporal_score) for r in seq_results[:2]]}")

        all_results.extend(seq_results)
        total_detections += len(seq_results)
        logger.info(f"序列 {seq_id} 检测完成，检测到 {len(seq_results)} 个目标")

        # 如果没有检测到目标，记录警告
        if len(seq_results) == 0:
            logger.warning(f"⚠️ 序列 {seq_id} 没有检测到任何目标，这可能影响评估结果")

    logger.info(f"所有序列检测完成，总共检测到 {total_detections} 个目标")

    # 保存检测结果
    save_results(all_results, args.output_path)

    # 评估检测结果
    if args.annotation_path:
        logger.info("开始评估检测结果")

        # 加载类别名称
        class_map, _ = load_class_map(args.class_json)
        class_names = list(class_map.values())

        # 初始化改进的评估器
        evaluator = ImprovedDetectionEvaluator(
            iou_threshold=args.iou_threshold,
            consistency_iou_threshold=args.consistency_iou_threshold,
            stability_threshold=args.stability_threshold,
            class_names=class_names
        )

        if os.path.isdir(args.annotation_path):
            # 加载所有指定序列的标注（包括没有检测到目标的序列）
            target_sequences = [seq.name for seq in sequences]  # 使用所有指定的序列
            ground_truth = load_yolo_ground_truth_with_natural_sort(
                args.annotation_path, args.class_json, args.data_path,
                args.sample_ratio, target_sequences
            )
        else:
            logger.error("标注路径必须是目录")
            return

        predictions_by_seq = defaultdict(list)
        for result in all_results:
            predictions_by_seq[result.sequence_id].append(result)

        # 确保所有指定的序列都在predictions_by_seq中，即使没有检测结果
        for seq in sequences:
            if seq.name not in predictions_by_seq:
                predictions_by_seq[seq.name] = []  # 空列表表示没有检测到目标

        # 显示评估前的统计信息
        print(f"\n=== 评估前统计信息 ===")
        print(f"指定的序列: {[seq.name for seq in sequences]}")
        print(f"有检测结果的序列: {[seq_id for seq_id, results in predictions_by_seq.items() if len(results) > 0]}")
        print(f"没有检测结果的序列: {[seq_id for seq_id, results in predictions_by_seq.items() if len(results) == 0]}")
        print(f"有标注数据的序列: {list(ground_truth.keys())}")
        print(f"将要评估的序列: {list(predictions_by_seq.keys())}")

        print(f"\n开始评估 {len(predictions_by_seq)} 个序列的检测结果...")
        evaluation_results = evaluator.evaluate_all_sequences(predictions_by_seq, ground_truth)

        logger.info("所有序列评估完成")

        # 使用改进的结果显示方式
        evaluator.print_overall_results(evaluation_results['overall_results'])

        # 保存评估结果
        eval_output_path = args.output_path.replace('.json', '_evaluation_improved.json')
        output_dir = os.path.dirname(eval_output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        with open(eval_output_path, 'w', encoding='utf-8') as f:
            json.dump(evaluation_results, f, ensure_ascii=False, indent=2)
        logger.info(f"评估结果已保存到: {eval_output_path}")

if __name__ == "__main__":
    main()
