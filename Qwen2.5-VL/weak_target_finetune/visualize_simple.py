#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的检测结果可视化工具
只使用PIL，不依赖其他库
"""

import os
import json
from pathlib import Path
from typing import List, Dict, Tuple
import argparse
from collections import defaultdict
from PIL import Image, ImageDraw, ImageFont

class SimpleDetectionVisualizer:
    """简单的检测结果可视化器"""
    
    def __init__(self, output_dir: str = "visual_result"):
        """初始化可视化器"""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 定义类别颜色映射 (RGB格式)
        self.class_colors = {
            'drone': (0, 255, 0),      # 绿色
            'car': (255, 0, 0),        # 红色
            'ship': (0, 0, 255),       # 蓝色
            'bus': (255, 255, 0),      # 黄色
            'pedestrian': (255, 0, 255), # 洋红色
            'cyclist': (0, 255, 255),  # 青色
            'unknown': (128, 128, 128) # 灰色
        }
        
        # 默认颜色
        self.default_color = (255, 255, 255)  # 白色
        
        # 尝试加载字体
        try:
            self.font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 16)
        except:
            self.font = ImageFont.load_default()
        
    def get_color_for_class(self, class_name: str) -> Tuple[int, int, int]:
        """获取类别对应的颜色"""
        return self.class_colors.get(class_name, self.default_color)
    
    def draw_bbox(self, image: Image.Image, bbox: List[float], label: str, 
                  confidence: float, temporal_score: float = None) -> Image.Image:
        """在图像上绘制边界框"""
        # 创建绘图对象
        draw = ImageDraw.Draw(image)
        
        # 确保bbox坐标是整数
        x1, y1, x2, y2 = [int(coord) for coord in bbox[:4]]
        
        # 获取类别颜色
        color = self.get_color_for_class(label)
        
        # 绘制边界框
        draw.rectangle([x1, y1, x2, y2], outline=color, width=2)
        
        # 准备标签文本
        if temporal_score is not None:
            text = f"{label}: {confidence:.2f} (T:{temporal_score:.2f})"
        else:
            text = f"{label}: {confidence:.2f}"
        
        # 计算文本尺寸
        bbox_text = draw.textbbox((0, 0), text, font=self.font)
        text_width = bbox_text[2] - bbox_text[0]
        text_height = bbox_text[3] - bbox_text[1]
        
        # 绘制文本背景
        draw.rectangle([x1, y1 - text_height - 4, x1 + text_width + 4, y1], 
                      fill=color)
        
        # 绘制文本
        draw.text((x1 + 2, y1 - text_height - 2), text, fill=(0, 0, 0), font=self.font)
        
        return image
    
    def visualize_frame(self, image_path: str, detections: List[Dict], 
                       output_path: str) -> bool:
        """可视化单帧检测结果"""
        try:
            # 读取图像
            if not os.path.exists(image_path):
                print(f"警告: 图像文件不存在: {image_path}")
                return False
                
            image = Image.open(image_path)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 在图像上绘制所有检测结果
            for detection in detections:
                bbox = detection['bbox']
                label = detection['label']
                confidence = detection['confidence']
                temporal_score = detection.get('temporal_score', None)
                
                image = self.draw_bbox(image, bbox, label, confidence, temporal_score)
            
            # 在图像上添加统计信息
            draw = ImageDraw.Draw(image)
            stats_text = f"Detections: {len(detections)}"
            draw.text((10, 10), stats_text, fill=(255, 255, 255), font=self.font)
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 保存图像
            image.save(output_path, 'JPEG', quality=95)
            return True
                
        except Exception as e:
            print(f"错误: 可视化帧时出错: {e}")
            return False
    
    def find_image_file(self, data_path: str, seq_id: str, frame_id: str) -> str:
        """查找对应的图像文件"""
        seq_dir = Path(data_path) / seq_id
        if not seq_dir.exists():
            return None
        
        # 尝试不同的文件扩展名
        extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        
        # 首先尝试直接匹配frame_id
        for ext in extensions:
            candidate = seq_dir / f"{frame_id}{ext}"
            if candidate.exists():
                return str(candidate)
        
        # 如果直接匹配失败，尝试按索引查找
        try:
            frame_idx = int(frame_id)
            
            # 获取所有图像文件并排序
            image_files = []
            for ext in extensions:
                image_files.extend(seq_dir.glob(f'*{ext}'))
            
            if not image_files:
                return None
            
            # 使用自然排序
            def natural_sort_key(filename: str):
                import re
                numbers = re.findall(r'\d+', filename)
                if numbers:
                    return [int(numbers[-1])]
                else:
                    return [filename.lower()]
            
            image_files.sort(key=lambda x: natural_sort_key(x.name))
            
            # 按索引返回对应文件
            if frame_idx < len(image_files):
                return str(image_files[frame_idx])
                
        except ValueError:
            pass
        
        return None
    
    def visualize_detection_results(self, detection_results_path: str, 
                                   data_path: str, sequences: List[str] = None) -> Dict:
        """可视化所有检测结果"""
        print("开始可视化检测结果...")
        
        # 加载检测结果
        try:
            with open(detection_results_path, 'r', encoding='utf-8') as f:
                detection_data = json.load(f)
        except Exception as e:
            print(f"错误: 无法加载检测结果文件: {e}")
            return {}
        
        # 按序列和帧分组检测结果
        detections_by_seq_frame = defaultdict(lambda: defaultdict(list))
        
        for detection in detection_data:
            seq_id = detection['sequence_id']
            frame_id = detection['frame_id']
            
            # 如果指定了序列列表，只处理这些序列
            if sequences and seq_id not in sequences:
                continue
                
            detections_by_seq_frame[seq_id][frame_id].append(detection)
        
        # 统计信息
        stats = {
            'total_sequences': len(detections_by_seq_frame),
            'total_frames': 0,
            'total_detections': len(detection_data),
            'successful_visualizations': 0,
            'failed_visualizations': 0,
            'sequence_stats': {}
        }
        
        # 处理每个序列
        for seq_id, frames_data in detections_by_seq_frame.items():
            print(f"处理序列: {seq_id}")
            
            # 创建序列输出目录
            seq_output_dir = self.output_dir / seq_id
            seq_output_dir.mkdir(exist_ok=True)
            
            seq_stats = {
                'frames': len(frames_data),
                'detections': sum(len(dets) for dets in frames_data.values()),
                'successful': 0,
                'failed': 0
            }
            
            # 处理每一帧
            for frame_id, frame_detections in frames_data.items():
                # 构建图像文件路径
                image_path = self.find_image_file(data_path, seq_id, frame_id)
                
                if image_path is None:
                    print(f"警告: 找不到图像文件: 序列={seq_id}, 帧={frame_id}")
                    seq_stats['failed'] += 1
                    continue
                
                # 构建输出文件路径
                output_filename = f"frame_{int(frame_id):06d}.jpg"
                output_path = seq_output_dir / output_filename
                
                # 可视化当前帧
                success = self.visualize_frame(image_path, frame_detections, str(output_path))
                
                if success:
                    seq_stats['successful'] += 1
                else:
                    seq_stats['failed'] += 1
            
            stats['sequence_stats'][seq_id] = seq_stats
            stats['total_frames'] += seq_stats['frames']
            stats['successful_visualizations'] += seq_stats['successful']
            stats['failed_visualizations'] += seq_stats['failed']
            
            print(f"序列 {seq_id} 完成: {seq_stats['successful']}/{seq_stats['frames']} 帧成功")
        
        print("可视化完成!")
        print(f"总计: {stats['successful_visualizations']}/{stats['total_frames']} 帧成功")
        
        return stats

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='简单检测结果可视化工具')
    parser.add_argument('--detection_results', type=str, required=True, 
                       help='检测结果JSON文件路径')
    parser.add_argument('--data_path', type=str, required=True, 
                       help='图像数据根目录')
    parser.add_argument('--output_dir', type=str, default='visual_result', 
                       help='输出目录（默认：visual_result）')
    parser.add_argument('--sequences', type=str, nargs='*', 
                       help='要可视化的序列名称，不指定则可视化所有序列')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.detection_results):
        print(f"错误: 检测结果文件不存在: {args.detection_results}")
        return
    
    if not os.path.exists(args.data_path):
        print(f"错误: 数据目录不存在: {args.data_path}")
        return
    
    # 创建可视化器
    visualizer = SimpleDetectionVisualizer(args.output_dir)
    
    # 执行可视化
    stats = visualizer.visualize_detection_results(
        args.detection_results, 
        args.data_path, 
        args.sequences
    )
    
    # 打印统计信息
    print("\n" + "="*60)
    print("可视化统计信息")
    print("="*60)
    print(f"处理序列数: {stats['total_sequences']}")
    print(f"总帧数: {stats['total_frames']}")
    print(f"总检测数: {stats['total_detections']}")
    print(f"成功可视化: {stats['successful_visualizations']}")
    print(f"失败数量: {stats['failed_visualizations']}")
    
    if stats['sequence_stats']:
        print(f"\n各序列详情:")
        for seq_id, seq_stats in stats['sequence_stats'].items():
            success_rate = seq_stats['successful'] / seq_stats['frames'] * 100 if seq_stats['frames'] > 0 else 0
            print(f"  {seq_id}: {seq_stats['successful']}/{seq_stats['frames']} 帧 "
                  f"({success_rate:.1f}%), {seq_stats['detections']} 个检测")
    
    print(f"\n可视化结果保存在: {args.output_dir}")
    print("="*60)

if __name__ == "__main__":
    main()
