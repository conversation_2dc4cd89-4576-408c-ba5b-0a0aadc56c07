#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据集中图像的实际尺寸
分析哪些图像需要坐标修正
"""

import os
from PIL import Image
from pathlib import Path
from collections import defaultdict
import math

def smart_resize(height: int, width: int, factor: int = 28, min_pixels: int = 56 * 56, max_pixels: int = 14 * 14 * 4 * 1280):
    """Qwen2.5-VL的智能尺寸调整函数"""
    if height < factor or width < factor:
        raise ValueError(f"height:{height} or width:{width} must be larger than factor:{factor}")
    elif max(height, width) / min(height, width) > 200:
        raise ValueError(
            f"absolute aspect ratio must be smaller than 200, got {max(height, width) / min(height, width)}"
        )
    h_bar = round(height / factor) * factor
    w_bar = round(width / factor) * factor
    if h_bar * w_bar > max_pixels:
        beta = math.sqrt((height * width) / max_pixels)
        h_bar = math.floor(height / beta / factor) * factor
        w_bar = math.floor(width / beta / factor) * factor
    elif h_bar * w_bar < min_pixels:
        beta = math.sqrt(min_pixels / (height * width))
        h_bar = math.ceil(height * beta / factor) * factor
        w_bar = math.ceil(width * beta / factor) * factor
    return h_bar, w_bar

def check_image_sizes(data_paths):
    """检查数据集中的图像尺寸"""
    print("🔍 检查数据集中的图像尺寸")
    print("="*60)
    
    size_stats = defaultdict(int)
    resize_needed = defaultdict(int)
    total_images = 0
    
    for data_path in data_paths:
        if not os.path.exists(data_path):
            print(f"❌ 路径不存在: {data_path}")
            continue
            
        print(f"\n📁 检查路径: {data_path}")
        
        # 遍历所有序列
        for seq_dir in Path(data_path).iterdir():
            if not seq_dir.is_dir():
                continue
                
            print(f"\n📂 序列: {seq_dir.name}")
            
            # 获取该序列的前几个图像文件
            image_files = []
            for ext in ['.bmp', '.jpg', '.jpeg', '.png']:
                image_files.extend(seq_dir.glob(f'*{ext}'))
            
            if not image_files:
                print(f"  ❌ 没有找到图像文件")
                continue
            
            # 检查前5个图像的尺寸
            sample_files = sorted(image_files)[:5]
            
            for img_file in sample_files:
                try:
                    with Image.open(img_file) as img:
                        width, height = img.size
                        total_images += 1
                        
                        # 记录尺寸统计
                        size_key = f"{width}×{height}"
                        size_stats[size_key] += 1
                        
                        # 检查是否需要resize
                        try:
                            new_height, new_width = smart_resize(height, width)
                            needs_resize = (new_width != width or new_height != height)
                            
                            if needs_resize:
                                resize_needed[size_key] += 1
                                scale_w = new_width / width
                                scale_h = new_height / height
                                print(f"  📏 {img_file.name}: {width}×{height} → {new_width}×{new_height} "
                                      f"(缩放: {scale_w:.3f}×{scale_h:.3f}) 🔧需要修正")
                            else:
                                print(f"  📏 {img_file.name}: {width}×{height} ✅无需调整")
                                
                        except Exception as e:
                            print(f"  ❌ {img_file.name}: {width}×{height} - 调整失败: {e}")
                            
                except Exception as e:
                    print(f"  ❌ 无法读取 {img_file.name}: {e}")
            
            # 只检查每个序列的前几个文件，避免输出过多
            break
    
    # 显示统计结果
    print(f"\n" + "="*60)
    print(f"📊 图像尺寸统计")
    print(f"="*60)
    print(f"总检查图像数: {total_images}")
    
    print(f"\n📏 发现的图像尺寸:")
    for size, count in sorted(size_stats.items()):
        needs_correction = resize_needed[size] > 0
        status = "🔧需要坐标修正" if needs_correction else "✅无需修正"
        print(f"  {size}: {count} 张图像 - {status}")
    
    # 分析28的倍数
    print(f"\n🔢 28的倍数分析:")
    for size, count in sorted(size_stats.items()):
        width, height = map(int, size.split('×'))
        width_mod = width % 28
        height_mod = height % 28
        is_multiple = (width_mod == 0 and height_mod == 0)
        
        print(f"  {size}: 宽度余数={width_mod}, 高度余数={height_mod} - "
              f"{'✅是28的倍数' if is_multiple else '❌不是28的倍数'}")
    
    # 计算需要修正的比例
    total_needing_correction = sum(resize_needed.values())
    correction_ratio = total_needing_correction / total_images if total_images > 0 else 0
    
    print(f"\n🎯 坐标修正需求:")
    print(f"需要坐标修正的图像: {total_needing_correction}/{total_images} ({correction_ratio:.1%})")
    
    if correction_ratio > 0:
        print(f"💡 建议使用坐标修正版本: evaluate_finetuned_bbox_corrected.py")
    else:
        print(f"💡 可以使用原版本: evaluate_finetuned_improved.py")

def main():
    """主函数"""
    # 检查常见的数据路径
    data_paths = [
        "/home/<USER>/Qwen/Qwen2.5-VL/new_processed_data",
        "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images",
        "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/images"
    ]
    
    print("🔍 自动检测数据集图像尺寸")
    print("分析是否需要坐标修正")
    print("="*60)
    
    existing_paths = [path for path in data_paths if os.path.exists(path)]
    
    if not existing_paths:
        print("❌ 未找到任何数据路径")
        print("请检查以下路径是否存在:")
        for path in data_paths:
            print(f"  - {path}")
        return
    
    print(f"✅ 找到 {len(existing_paths)} 个数据路径")
    
    check_image_sizes(existing_paths)

if __name__ == "__main__":
    main()
