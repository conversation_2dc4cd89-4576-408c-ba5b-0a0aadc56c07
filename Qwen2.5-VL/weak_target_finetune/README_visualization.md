# 检测结果可视化工具

## 概述

这个工具可以将检测结果绘制在原始图像上，生成带有边界框和标签的可视化图像，帮助您直观地查看检测效果。

## 文件说明

- `visualize_detection_results.py` - 主要的可视化脚本
- `run_visualization.py` - 快速运行脚本（自动查找最新检测结果）
- `test_visualization.py` - 测试脚本
- `visual_result/` - 可视化结果输出目录

## 功能特性

### 🎨 可视化功能
- 在图像上绘制检测边界框
- 显示类别标签和置信度
- 显示时序得分（如果有）
- 不同类别使用不同颜色
- 显示每帧检测数量统计

### 🎯 类别颜色映射
- `drone` (无人机): 绿色
- `car` (汽车): 蓝色  
- `ship` (轮船): 红色
- `bus` (公交车): 青色
- `pedestrian` (行人): 洋红色
- `cyclist` (骑行者): 黄色
- `unknown` (未知): 灰色

### 📁 输出组织
```
visual_result/
├── data03/
│   ├── frame_000000.jpg
│   ├── frame_000001.jpg
│   └── ...
├── data05/
│   ├── frame_000000.jpg
│   └── ...
└── ...
```

## 使用方法

### 方法1：快速运行（推荐）

```bash
# 在Qwen虚拟环境中运行
conda activate Qwen
cd /home/<USER>/Qwen/Qwen2.5-VL/weak_target_finetune/

# 自动使用最新的检测结果
python run_visualization.py
```

### 方法2：手动指定参数

```bash
# 基本用法
python visualize_detection_results.py \
    --detection_results "./Output/detection_results.json" \
    --data_path "/home/<USER>/Qwen/Qwen2.5-VL/new_processed_data" \
    --output_dir "visual_result"

# 只可视化特定序列
python visualize_detection_results.py \
    --detection_results "./Output/detection_results.json" \
    --data_path "/home/<USER>/Qwen/Qwen2.5-VL/new_processed_data" \
    --output_dir "visual_result" \
    --sequences data03 data05 data20

# 显示详细日志
python visualize_detection_results.py \
    --detection_results "./Output/detection_results.json" \
    --data_path "/home/<USER>/Qwen/Qwen2.5-VL/new_processed_data" \
    --output_dir "visual_result" \
    --verbose
```

### 参数说明

- `--detection_results`: 检测结果JSON文件路径（必需）
- `--data_path`: 图像数据根目录（必需）
- `--output_dir`: 输出目录（默认：visual_result）
- `--sequences`: 要可视化的序列名称（可选，不指定则处理所有序列）
- `--verbose`: 显示详细日志（可选）

## 输入格式要求

### 检测结果JSON格式
```json
[
  {
    "sequence_id": "data03",
    "frame_id": "0",
    "bbox": [100, 100, 200, 200],
    "label": "drone",
    "confidence": 0.85,
    "temporal_score": 0.8
  },
  ...
]
```

### 图像数据目录结构
```
data_path/
├── data03/
│   ├── 0.jpg
│   ├── 1.jpg
│   └── ...
├── data05/
│   ├── 0.jpg
│   └── ...
└── ...
```

## 测试功能

```bash
# 创建测试数据并查看使用说明
python test_visualization.py
```

## 输出示例

运行完成后会显示统计信息：

```
============================================================
可视化统计信息
============================================================
处理序列数: 3
总帧数: 150
总检测数: 245
成功可视化: 148
失败数量: 2

各序列详情:
  data03: 48/50 帧 (96.0%), 85 个检测
  data05: 50/50 帧 (100.0%), 92 个检测
  data20: 50/50 帧 (100.0%), 68 个检测

可视化结果保存在: visual_result
============================================================
```

## 故障排除

### 常见问题

1. **找不到图像文件**
   - 检查 `--data_path` 是否正确
   - 确认图像文件扩展名（支持 .jpg, .jpeg, .png, .bmp）
   - 检查序列目录名是否与检测结果中的 `sequence_id` 匹配

2. **检测结果格式错误**
   - 确认JSON文件格式正确
   - 检查bbox坐标是否为4个数值
   - 确认必需字段都存在

3. **输出目录权限问题**
   - 确保有写入权限
   - 尝试使用绝对路径

### 调试技巧

```bash
# 使用详细日志查看详细信息
python visualize_detection_results.py --verbose ...

# 只处理一个序列进行测试
python visualize_detection_results.py --sequences data03 ...
```

## 依赖要求

- Python 3.8+
- OpenCV (cv2)
- NumPy
- Pathlib

在Qwen虚拟环境中这些依赖应该已经安装。

## 注意事项

1. 确保在Qwen虚拟环境中运行
2. 大量图像可视化可能需要较长时间
3. 输出图像会覆盖同名文件
4. 建议先用少量数据测试
