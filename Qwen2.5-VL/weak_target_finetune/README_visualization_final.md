# 检测结果可视化工具 - 使用指南

## 🎯 功能概述

这个工具可以将检测结果绘制在原始图像上，生成带有边界框和标签的可视化图像，帮助您直观地查看检测效果。

## 📁 文件说明

- `visualize_simple.py` - 主要的可视化脚本（推荐使用）
- `run_visualization_final.py` - 一键运行脚本
- `visual_result/` - 可视化结果输出目录

## 🚀 快速开始

### 方法1：一键运行（最简单）

```bash
# 在Qwen虚拟环境中运行
conda activate Qwen
cd /home/<USER>/Qwen/Qwen2.5-VL/weak_target_finetune/

# 一键可视化最新的检测结果
python run_visualization_final.py
```

### 方法2：手动指定参数

```bash
# 基本用法
python visualize_simple.py \
    --detection_results "./Output/newdata_detection_results.json" \
    --data_path "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images" \
    --output_dir "visual_result"

# 只可视化特定序列
python visualize_simple.py \
    --detection_results "./Output/newdata_detection_results.json" \
    --data_path "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images" \
    --output_dir "visual_result" \
    --sequences data_02 data_05 data_06
```

## 🎨 可视化效果

### 边界框颜色
- **drone** (无人机): 绿色
- **car** (汽车): 红色  
- **ship** (轮船): 蓝色
- **bus** (公交车): 黄色
- **pedestrian** (行人): 洋红色
- **cyclist** (骑行者): 青色
- **unknown** (未知): 灰色

### 标签信息
每个检测框会显示：
- 类别名称
- 置信度
- 时序得分（如果有）

例如：`drone: 0.85 (T:0.80)`

## 📂 输出结构

```
visual_result/
├── data_02/
│   ├── frame_000000.jpg
│   ├── frame_000001.jpg
│   ├── frame_000002.jpg
│   └── ...
├── data_05/
│   ├── frame_000000.jpg
│   └── ...
├── data_06/
├── data_14/
└── data_15/
```

## 📊 运行示例

```bash
$ python run_visualization_final.py

🎨 检测结果可视化工具
==================================================
📁 使用检测结果文件: ./Output/newdata_detection_results.json
📁 使用图像数据目录: /home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images
📁 输出目录: visual_result

🚀 执行命令:
   python visualize_simple.py --detection_results "./Output/newdata_detection_results.json" --data_path "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images" --output_dir "visual_result"

==================================================
开始可视化检测结果...
处理序列: data_02
序列 data_02 完成: 24/24 帧成功
处理序列: data_05
序列 data_05 完成: 29/29 帧成功
处理序列: data_06
序列 data_06 完成: 20/20 帧成功
处理序列: data_14
序列 data_14 完成: 75/75 帧成功
处理序列: data_15
序列 data_15 完成: 52/52 帧成功
可视化完成!
总计: 200/200 帧成功

============================================================
可视化统计信息
============================================================
处理序列数: 5
总帧数: 200
总检测数: 256
成功可视化: 200
失败数量: 0

各序列详情:
  data_02: 24/24 帧 (100.0%), 24 个检测
  data_05: 29/29 帧 (100.0%), 56 个检测
  data_06: 20/20 帧 (100.0%), 20 个检测
  data_14: 75/75 帧 (100.0%), 104 个检测
  data_15: 52/52 帧 (100.0%), 52 个检测

可视化结果保存在: visual_result
============================================================

🎉 可视化完成!
📁 结果保存在: visual_result/
📋 目录结构:
  📂 data_02/ (24 帧)
  📂 data_05/ (29 帧)
  📂 data_06/ (20 帧)
  📂 data_14/ (75 帧)
  📂 data_15/ (52 帧)

💡 您可以使用图像查看器打开 visual_result/ 目录查看可视化结果
```

## 🔧 参数说明

### visualize_simple.py 参数

- `--detection_results`: 检测结果JSON文件路径（必需）
- `--data_path`: 图像数据根目录（必需）
- `--output_dir`: 输出目录（默认：visual_result）
- `--sequences`: 要可视化的序列名称（可选）

## 📋 输入格式要求

### 检测结果JSON格式
```json
[
  {
    "sequence_id": "data_02",
    "frame_id": "0",
    "bbox": [100, 100, 200, 200],
    "label": "drone",
    "confidence": 0.85,
    "temporal_score": 0.8
  }
]
```

### 图像数据目录结构
```
data_path/
├── data_02/
│   ├── 0.jpg
│   ├── 1.jpg
│   └── ...
├── data_05/
└── ...
```

## 🛠️ 故障排除

### 常见问题

1. **找不到检测结果文件**
   ```bash
   # 检查Output目录
   ls -la Output/
   
   # 确保已运行检测
   python evaluate_finetuned_improved.py ...
   ```

2. **找不到图像文件**
   ```bash
   # 检查数据路径
   ls -la /home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images/
   ```

3. **权限问题**
   ```bash
   # 确保有写入权限
   chmod 755 visual_result/
   ```

### 调试技巧

```bash
# 只处理一个序列进行测试
python visualize_simple.py \
    --detection_results "./Output/newdata_detection_results.json" \
    --data_path "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images" \
    --sequences data_02
```

## 💡 使用建议

1. **首次使用**：建议先用一个序列测试
2. **大量数据**：可视化大量图像需要时间，请耐心等待
3. **查看结果**：使用系统的图像查看器浏览结果
4. **存储空间**：确保有足够的磁盘空间存储可视化图像

## 🎯 成功案例

根据上面的运行示例，工具成功处理了：
- **5个序列**：data_02, data_05, data_06, data_14, data_15
- **200帧图像**：100%成功率
- **256个检测结果**：全部可视化

每个序列的检测结果都被正确绘制在对应的图像上，并按帧序号保存在相应的子目录中。

## 📞 技术支持

如果遇到问题，请检查：
1. 是否在Qwen虚拟环境中运行
2. 检测结果文件是否存在且格式正确
3. 图像数据路径是否正确
4. 是否有足够的磁盘空间和权限
