#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量可视化所有序列的检测结果和真实标注
覆盖原来的可视化结果
"""

import os
import json
import sys
from PIL import Image, ImageDraw, ImageFont
from pathlib import Path
from collections import defaultdict

# 添加父目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from evaluate_finetuned_improved import load_yolo_ground_truth_with_natural_sort, load_class_map

class BatchVisualizer:
    """批量可视化器"""
    
    def __init__(self):
        """初始化"""
        # 定义颜色
        self.detection_color = (0, 255, 0)    # 绿色 - 检测框
        self.groundtruth_color = (255, 0, 0)  # 红色 - 真实标注框
        self.matched_color = (0, 255, 255)    # 青色 - 匹配的框
        
        # 尝试加载字体
        try:
            self.font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 14)
            self.small_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 12)
        except:
            self.font = ImageFont.load_default()
            self.small_font = ImageFont.load_default()
    
    def calculate_iou(self, box1, box2):
        """计算IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        inter_x1 = max(x1_1, x1_2)
        inter_y1 = max(y1_1, y1_2)
        inter_x2 = min(x2_1, x2_2)
        inter_y2 = min(y2_1, y2_2)
        
        if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
            return 0.0
        
        inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def draw_dashed_rectangle(self, draw, bbox, color, width=2, dash_length=3):
        """绘制虚线矩形"""
        x1, y1, x2, y2 = bbox
        
        # 上边
        for x in range(x1, x2, dash_length * 2):
            draw.line([x, y1, min(x + dash_length, x2), y1], fill=color, width=width)
        
        # 下边
        for x in range(x1, x2, dash_length * 2):
            draw.line([x, y2, min(x + dash_length, x2), y2], fill=color, width=width)
        
        # 左边
        for y in range(y1, y2, dash_length * 2):
            draw.line([x1, y, x1, min(y + dash_length, y2)], fill=color, width=width)
        
        # 右边
        for y in range(y1, y2, dash_length * 2):
            draw.line([x2, y, x2, min(y + dash_length, y2)], fill=color, width=width)
    
    def find_image_file(self, data_path: str, seq_id: str, frame_id: str) -> str:
        """查找对应的图像文件"""
        seq_dir = Path(data_path) / seq_id
        if not seq_dir.exists():
            return None
        
        # 尝试不同的文件扩展名
        extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        
        # 首先尝试直接匹配frame_id
        for ext in extensions:
            candidate = seq_dir / f"{frame_id}{ext}"
            if candidate.exists():
                return str(candidate)
        
        # 如果直接匹配失败，尝试按索引查找
        try:
            frame_idx = int(frame_id)
            
            # 获取所有图像文件并排序
            image_files = []
            for ext in extensions:
                image_files.extend(seq_dir.glob(f'*{ext}'))
            
            if not image_files:
                return None
            
            # 使用自然排序
            def natural_sort_key(filename: str):
                import re
                numbers = re.findall(r'\d+', filename)
                if numbers:
                    return [int(numbers[-1])]
                else:
                    return [filename.lower()]
            
            image_files.sort(key=lambda x: natural_sort_key(x.name))
            
            # 按索引返回对应文件
            if frame_idx < len(image_files):
                return str(image_files[frame_idx])
                
        except ValueError:
            pass
        
        return None
    
    def visualize_frame(self, image_path: str, detections: list, ground_truths: list, 
                       output_path: str, seq_id: str, frame_id: str, iou_threshold: float = 0.3):
        """可视化单帧"""
        try:
            # 读取图像
            if not os.path.exists(image_path):
                print(f"警告: 图像文件不存在: {image_path}")
                return False
                
            image = Image.open(image_path)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            draw = ImageDraw.Draw(image)
            
            # 匹配检测框和真实框
            matched_dets = set()
            matched_gts = set()
            matches = []
            
            for i, det in enumerate(detections):
                best_iou = 0
                best_gt_idx = -1
                
                for j, gt in enumerate(ground_truths):
                    if j in matched_gts:
                        continue
                    
                    # 只匹配相同类别
                    if det['label'] != gt['label']:
                        continue
                    
                    iou = self.calculate_iou(det['bbox'], gt['bbox'])
                    if iou > best_iou:
                        best_iou = iou
                        best_gt_idx = j
                
                if best_iou >= iou_threshold and best_gt_idx not in matched_gts:
                    matched_dets.add(i)
                    matched_gts.add(best_gt_idx)
                    matches.append((i, best_gt_idx, best_iou))
            
            # 绘制真实标注框（红色虚线）
            for j, gt in enumerate(ground_truths):
                color = self.matched_color if j in matched_gts else self.groundtruth_color
                bbox = [int(x) for x in gt['bbox']]
                self.draw_dashed_rectangle(draw, bbox, color, width=2)
                
                # 添加标签
                label_text = f"GT:{gt['label']}"
                draw.text((bbox[0], bbox[1] - 15), label_text, fill=color, font=self.small_font)
            
            # 绘制检测框（绿色实线）
            for i, det in enumerate(detections):
                color = self.matched_color if i in matched_dets else self.detection_color
                bbox = [int(x) for x in det['bbox']]
                draw.rectangle(bbox, outline=color, width=2)
                
                # 添加标签
                confidence = det.get('confidence', 0.0)
                temporal_score = det.get('temporal_score', 0.0)
                label_text = f"DET:{det['label']}({confidence:.2f})"
                draw.text((bbox[0], bbox[1] - 30), label_text, fill=color, font=self.small_font)
            
            # 添加统计信息
            info_text = [
                f"{seq_id} - 帧{frame_id}",
                f"检测: {len(detections)} | 标注: {len(ground_truths)} | 匹配: {len(matches)}",
                f"IoU阈值: {iou_threshold}",
                "绿色实线=检测框, 红色虚线=标注框, 青色=匹配"
            ]
            
            y_offset = 10
            for text in info_text:
                # 绘制文本背景
                bbox_text = draw.textbbox((0, 0), text, font=self.font)
                text_width = bbox_text[2] - bbox_text[0]
                text_height = bbox_text[3] - bbox_text[1]
                draw.rectangle([10, y_offset, 10 + text_width + 4, y_offset + text_height + 2], 
                             fill=(0, 0, 0, 128))
                
                draw.text((12, y_offset + 1), text, fill=(255, 255, 255), font=self.font)
                y_offset += text_height + 5
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 保存图像
            image.save(output_path, 'JPEG', quality=95)
            return True
                
        except Exception as e:
            print(f"错误: 可视化帧时出错: {e}")
            return False
    
    def visualize_all_sequences(self, detection_results_path: str, annotation_path: str, 
                               data_path: str, class_json_path: str, output_base_dir: str = "visual_result_new"):
        """可视化所有序列"""
        print("🎨 批量可视化所有序列的检测结果和真实标注")
        print("="*80)
        
        # 加载检测结果
        with open(detection_results_path, 'r', encoding='utf-8') as f:
            detection_data = json.load(f)
        
        # 获取所有序列
        sequences = list(set(d['sequence_id'] for d in detection_data))
        sequences.sort()
        
        print(f"📊 找到 {len(sequences)} 个序列: {sequences}")
        
        # 加载真实标注
        ground_truth = load_yolo_ground_truth_with_natural_sort(
            annotation_path, class_json_path, data_path, 
            sample_ratio=0.05, target_sequences=sequences
        )
        
        # 按序列和帧分组数据
        detections_by_seq_frame = defaultdict(lambda: defaultdict(list))
        for detection in detection_data:
            seq_id = detection['sequence_id']
            frame_id = detection['frame_id']
            detections_by_seq_frame[seq_id][frame_id].append(detection)
        
        gt_by_seq_frame = defaultdict(lambda: defaultdict(list))
        for seq_id, gt_list in ground_truth.items():
            for gt in gt_list:
                frame_id = gt.frame_id
                gt_dict = {
                    'bbox': gt.bbox,
                    'label': gt.label
                }
                gt_by_seq_frame[seq_id][frame_id].append(gt_dict)
        
        # 处理每个序列
        total_processed = 0
        total_success = 0
        
        for seq_id in sequences:
            print(f"\n🔍 处理序列: {seq_id}")
            
            # 创建序列输出目录
            seq_output_dir = Path(output_base_dir) / seq_id
            seq_output_dir.mkdir(parents=True, exist_ok=True)
            
            # 获取该序列的所有帧
            det_frames = set(detections_by_seq_frame[seq_id].keys())
            gt_frames = set(gt_by_seq_frame[seq_id].keys())
            all_frames = det_frames | gt_frames
            
            seq_processed = 0
            seq_success = 0
            
            for frame_id in sorted(all_frames, key=lambda x: int(x) if x.isdigit() else 0):
                frame_detections = detections_by_seq_frame[seq_id][frame_id]
                frame_gts = gt_by_seq_frame[seq_id][frame_id]
                
                # 查找对应的图像文件
                image_path = self.find_image_file(data_path, seq_id, frame_id)
                
                if image_path is None:
                    continue
                
                # 构建输出文件路径
                output_filename = f"frame_{int(frame_id):06d}_comparison.jpg"
                output_path = seq_output_dir / output_filename
                
                # 可视化当前帧
                success = self.visualize_frame(
                    image_path, frame_detections, frame_gts, str(output_path), seq_id, frame_id
                )
                
                seq_processed += 1
                total_processed += 1
                
                if success:
                    seq_success += 1
                    total_success += 1
            
            print(f"  ✅ 序列 {seq_id}: {seq_success}/{seq_processed} 帧成功")
        
        print(f"\n🎉 批量可视化完成!")
        print(f"📊 总计: {total_success}/{total_processed} 帧成功")
        print(f"📁 结果保存在: {output_base_dir}/")
        print(f"💡 每个序列的可视化结果都包含检测框和真实标注框的对比")

def main():
    """主函数"""
    # 文件路径
    detection_results = "Output/newdata_detection_results_fixed2.json"
    annotation_path = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/labels"
    data_path = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images"
    class_json = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/class.json"
    output_dir = "visual_result_new"
    
    # 检查文件是否存在
    if not os.path.exists(detection_results):
        print(f"❌ 检测结果文件不存在: {detection_results}")
        return
    
    # 创建可视化器
    visualizer = BatchVisualizer()
    
    # 执行批量可视化
    visualizer.visualize_all_sequences(
        detection_results, annotation_path, data_path, class_json, output_dir
    )

if __name__ == "__main__":
    main()
