#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试坐标修正功能
验证Qwen2.5-VL模型的坐标转换是否正确
"""

import math
import sys
from pathlib import Path

# 添加父目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入坐标转换函数
from evaluate_finetuned_bbox_corrected import smart_resize, convert_bbox_to_original_coords, convert_bbox_to_model_coords

def test_smart_resize():
    """测试智能尺寸调整函数"""
    print("🔧 测试智能尺寸调整函数")
    print("="*50)
    
    test_cases = [
        (407, 663, "小尺寸图像"),
        (1080, 1920, "大尺寸图像"),
        (640, 640, "正方形图像"),
        (1024, 768, "中等尺寸图像"),
        (2048, 1536, "超大尺寸图像")
    ]
    
    for height, width, description in test_cases:
        new_height, new_width = smart_resize(height, width)
        scale_h = new_height / height
        scale_w = new_width / width
        
        print(f"\n📐 {description}:")
        print(f"  原始尺寸: {width} × {height}")
        print(f"  调整后尺寸: {new_width} × {new_height}")
        print(f"  缩放比例: {scale_w:.3f} × {scale_h:.3f}")
        print(f"  是否28的倍数: {new_width % 28 == 0 and new_height % 28 == 0}")
        print(f"  像素数: {new_width * new_height:,}")

def test_coordinate_conversion():
    """测试坐标转换功能"""
    print("\n🎯 测试坐标转换功能")
    print("="*50)
    
    # 测试案例：大尺寸图像中的目标框
    orig_width, orig_height = 1920, 1080
    
    # 模拟模型输出的坐标（基于调整后的图像尺寸）
    model_bbox = [959, 628, 1019, 649]  # 示例坐标
    
    print(f"📊 测试图像尺寸: {orig_width} × {orig_height}")
    print(f"🤖 模型输出坐标: {model_bbox}")
    
    # 计算模型内部调整后的尺寸
    new_height, new_width = smart_resize(orig_height, orig_width)
    print(f"🔧 模型内部尺寸: {new_width} × {new_height}")
    
    # 转换为原始图像坐标
    original_coords = convert_bbox_to_original_coords(model_bbox, orig_height, orig_width)
    print(f"📍 原始图像坐标: {original_coords}")
    
    # 验证反向转换
    model_coords_back = convert_bbox_to_model_coords(original_coords, orig_height, orig_width)
    print(f"🔄 反向转换验证: {model_coords_back}")
    
    # 检查转换精度
    diff = [abs(a - b) for a, b in zip(model_bbox, model_coords_back)]
    max_diff = max(diff)
    print(f"✅ 转换精度: 最大误差 {max_diff:.1f} 像素")
    
    if max_diff < 2:
        print("🎉 坐标转换精度良好！")
    else:
        print("⚠️ 坐标转换可能存在精度问题")

def test_different_image_sizes():
    """测试不同图像尺寸的坐标转换"""
    print("\n📏 测试不同图像尺寸的坐标转换")
    print("="*50)
    
    test_images = [
        (407, 663, [100, 100, 200, 200]),
        (1080, 1920, [500, 300, 600, 400]),
        (640, 640, [200, 200, 300, 300]),
        (2048, 1536, [1000, 800, 1200, 1000])
    ]
    
    for height, width, test_bbox in test_images:
        print(f"\n🖼️ 图像尺寸: {width} × {height}")
        print(f"📦 测试边界框: {test_bbox}")
        
        # 转换为模型坐标
        model_coords = convert_bbox_to_model_coords(test_bbox, height, width)
        print(f"🤖 模型坐标: {model_coords}")
        
        # 转换回原始坐标
        original_coords = convert_bbox_to_original_coords(model_coords, height, width)
        print(f"📍 恢复坐标: {original_coords}")
        
        # 计算误差
        diff = [abs(a - b) for a, b in zip(test_bbox, original_coords)]
        max_diff = max(diff)
        print(f"📊 转换误差: {diff} (最大: {max_diff:.1f})")
        
        # 计算调整后的图像尺寸
        new_height, new_width = smart_resize(height, width)
        scale_needed = new_width != width or new_height != height
        print(f"🔧 需要调整: {'是' if scale_needed else '否'}")

def test_edge_cases():
    """测试边界情况"""
    print("\n⚠️ 测试边界情况")
    print("="*50)
    
    # 测试边界框在图像边缘的情况
    test_cases = [
        (1920, 1080, [0, 0, 100, 100], "左上角"),
        (1920, 1080, [1820, 980, 1920, 1080], "右下角"),
        (1920, 1080, [960, 540, 960, 540], "单点（无面积）"),
        (1920, 1080, [100, 100, 1800, 900], "大边界框")
    ]
    
    for width, height, bbox, description in test_cases:
        print(f"\n🎯 {description}: {bbox}")
        
        try:
            model_coords = convert_bbox_to_model_coords(bbox, height, width)
            original_coords = convert_bbox_to_original_coords(model_coords, height, width)
            
            # 检查坐标是否在有效范围内
            x1, y1, x2, y2 = original_coords
            valid = (0 <= x1 <= width and 0 <= y1 <= height and 
                    0 <= x2 <= width and 0 <= y2 <= height and
                    x1 <= x2 and y1 <= y2)
            
            print(f"  模型坐标: {model_coords}")
            print(f"  恢复坐标: {original_coords}")
            print(f"  坐标有效: {'✅' if valid else '❌'}")
            
        except Exception as e:
            print(f"  ❌ 转换失败: {e}")

def main():
    """主函数"""
    print("🧪 Qwen2.5-VL坐标修正功能测试")
    print("="*60)
    
    try:
        test_smart_resize()
        test_coordinate_conversion()
        test_different_image_sizes()
        test_edge_cases()
        
        print("\n🎉 所有测试完成！")
        print("💡 如果所有测试都通过，说明坐标修正功能工作正常")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        print("🔧 请检查坐标转换函数的实现")

if __name__ == "__main__":
    main()
