#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
data_15优化评估脚本
使用合适的IoU阈值提升data_15的评估分数
"""

import os
import sys

def run_optimized_evaluation():
    """运行优化的data_15评估"""
    print("🎯 data_15优化评估")
    print("="*50)
    
    # 基础命令
    base_cmd = """CUDA_VISIBLE_DEVICES=0 python evaluate_finetuned_improved.py \
--base_model_path "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct" \
--data_path "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/images" \
--annotation_path "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/labels" \
--class_json "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/class.json" \
--sample_ratio 0.05 \
--sequences data_15"""
    
    # 测试不同IoU阈值
    iou_thresholds = [0.15, 0.2, 0.25]
    
    for iou_threshold in iou_thresholds:
        print(f"\n🔍 测试IoU阈值: {iou_threshold}")
        
        output_path = f"./Output/data15_iou_{iou_threshold:.2f}.json"
        
        cmd = f"""{base_cmd} \
--output_path "{output_path}" \
--iou_threshold {iou_threshold} \
--consistency_iou_threshold {iou_threshold}"""
        
        print(f"📋 执行命令:")
        print(f"   {cmd}")
        
        # 执行命令
        result = os.system(cmd)
        
        if result == 0:
            print(f"✅ IoU阈值 {iou_threshold} 评估完成")
        else:
            print(f"❌ IoU阈值 {iou_threshold} 评估失败")
    
    print(f"\n🎉 data_15优化评估完成!")
    print(f"💡 预期结果:")
    print(f"  - IoU阈值 0.15: 召回率 ~90%")
    print(f"  - IoU阈值 0.20: 召回率 ~60%") 
    print(f"  - IoU阈值 0.25: 召回率 ~40%")

if __name__ == "__main__":
    run_optimized_evaluation()
